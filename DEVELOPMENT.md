# ShareX Development Guide

This guide provides detailed instructions for setting up and developing the ShareX application.

## 🏗️ Development Setup

### Prerequisites

- **Docker & Docker Compose**: Latest version
- **Git**: For version control
- **Node.js 18+**: For local frontend development (optional)
- **Python 3.11+**: For local backend development (optional)
- **AWS Account**: For S3 storage (development bucket recommended)

### Environment Configuration

1. **Copy Environment File**
   ```bash
   cp .env.example .env
   ```

2. **Configure Environment Variables**
   
   Edit `.env` with your specific values:

   ```bash
   # Database Configuration
   DATABASE_URL=******************************************************/sharex_db
   POSTGRES_USER=sharex_user
   POSTGRES_PASSWORD=secure_password  # Change in production
   POSTGRES_DB=sharex_db
   
   # Redis Configuration
   REDIS_URL=redis://redis:6379/0
   
   # AWS S3 Configuration (REQUIRED)
   AWS_ACCESS_KEY_ID=your_access_key_here
   AWS_SECRET_ACCESS_KEY=your_secret_key_here
   AWS_S3_BUCKET_NAME=your-bucket-name
   AWS_REGION=your-region
   
   # JWT Configuration
   JWT_SECRET_KEY=your_super_secret_jwt_key_here  # Generate new key
   JWT_ALGORITHM=HS256
   JWT_ACCESS_TOKEN_EXPIRE_MINUTES=60
   
   # Application Configuration
   APP_ENV=development
   DEBUG=true
   ALLOWED_ORIGINS=http://localhost:3000
   
   # File Upload Configuration
   MAX_FILE_SIZE=10485760  # 10MB
   ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp
   ```

### AWS S3 Setup

1. **Create S3 Bucket**
   ```bash
   # Using AWS CLI
   aws s3 mb s3://your-sharex-dev-bucket --region your-region
   ```

2. **Configure Bucket Policy** (Optional - for public read access)
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Sid": "PublicReadGetObject",
         "Effect": "Allow",
         "Principal": "*",
         "Action": "s3:GetObject",
         "Resource": "arn:aws:s3:::your-sharex-dev-bucket/*"
       }
     ]
   }
   ```

3. **IAM User Permissions**
   Ensure your IAM user has these permissions:
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": [
           "s3:GetObject",
           "s3:PutObject",
           "s3:DeleteObject",
           "s3:ListBucket"
         ],
         "Resource": [
           "arn:aws:s3:::your-sharex-dev-bucket",
           "arn:aws:s3:::your-sharex-dev-bucket/*"
         ]
       }
     ]
   }
   ```

## 🚀 Running the Application

### Full Stack Development

1. **Start All Services**
   ```bash
   # Build and start all containers
   docker-compose up --build
   
   # Or run in background
   docker-compose up --build -d
   ```

2. **View Logs**
   ```bash
   # All services
   docker-compose logs -f
   
   # Specific service
   docker-compose logs -f backend
   docker-compose logs -f frontend
   ```

3. **Stop Services**
   ```bash
   docker-compose down
   
   # Remove volumes (careful - deletes data)
   docker-compose down -v
   ```

### Individual Service Development

#### Backend Only
```bash
# Start dependencies
docker-compose up postgres redis -d

# Run backend locally
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### Frontend Only
```bash
# Start backend services
docker-compose up backend postgres redis -d

# Run frontend locally
cd frontend
npm install
npm run dev
```

## 🗄️ Database Management

### Migrations

1. **Create Migration**
   ```bash
   # Inside backend container
   docker-compose exec backend alembic revision --autogenerate -m "Description"
   
   # Or locally
   cd backend
   alembic revision --autogenerate -m "Description"
   ```

2. **Apply Migrations**
   ```bash
   # Inside container
   docker-compose exec backend alembic upgrade head
   
   # Or locally
   cd backend
   alembic upgrade head
   ```

3. **Rollback Migration**
   ```bash
   # Rollback one version
   docker-compose exec backend alembic downgrade -1
   
   # Rollback to specific version
   docker-compose exec backend alembic downgrade <revision_id>
   ```

### Database Seeding

1. **Create Admin User**
   ```bash
   docker-compose exec backend python -c "
   from app.utils.seed import create_admin_user
   create_admin_user()
   "
   ```

2. **Seed Test Data**
   ```bash
   docker-compose exec backend python -c "
   from app.utils.seed import seed_test_data
   seed_test_data()
   "
   ```

### Database Access

1. **PostgreSQL CLI**
   ```bash
   docker-compose exec postgres psql -U sharex_user -d sharex_db
   ```

2. **Redis CLI**
   ```bash
   docker-compose exec redis redis-cli
   ```

## 🧪 Testing

### Backend Testing

1. **Run Tests**
   ```bash
   # Inside container
   docker-compose exec backend pytest
   
   # With coverage
   docker-compose exec backend pytest --cov=app
   
   # Specific test file
   docker-compose exec backend pytest tests/test_auth.py
   ```

2. **Test Database**
   ```bash
   # Create test database
   docker-compose exec postgres createdb -U sharex_user sharex_test_db
   ```

### Frontend Testing

1. **Run Tests**
   ```bash
   # Inside container
   docker-compose exec frontend npm test
   
   # Or locally
   cd frontend
   npm test
   ```

2. **E2E Testing**
   ```bash
   # Install Playwright
   cd frontend
   npm install @playwright/test
   
   # Run E2E tests
   npm run test:e2e
   ```

## 🔧 Development Tools

### API Documentation

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### Database Tools

- **pgAdmin**: Add to docker-compose for GUI access
- **Redis Commander**: Add to docker-compose for Redis GUI

### Code Quality

1. **Backend Linting**
   ```bash
   # Inside container
   docker-compose exec backend black app/
   docker-compose exec backend isort app/
   docker-compose exec backend flake8 app/
   ```

2. **Frontend Linting**
   ```bash
   # Inside container
   docker-compose exec frontend npm run lint
   docker-compose exec frontend npm run lint:fix
   ```

## 🐛 Debugging

### Backend Debugging

1. **Enable Debug Mode**
   ```python
   # In app/main.py
   import debugpy
   debugpy.listen(("0.0.0.0", 5678))
   debugpy.wait_for_client()
   ```

2. **VS Code Debug Configuration**
   ```json
   {
     "name": "Python: Remote Attach",
     "type": "python",
     "request": "attach",
     "connect": {
       "host": "localhost",
       "port": 5678
     },
     "pathMappings": [
       {
         "localRoot": "${workspaceFolder}/backend",
         "remoteRoot": "/app"
       }
     ]
   }
   ```

### Frontend Debugging

1. **Browser DevTools**: Standard React debugging
2. **React DevTools**: Install browser extension
3. **Redux DevTools**: For state management debugging

### Container Debugging

1. **Access Container Shell**
   ```bash
   # Backend
   docker-compose exec backend bash
   
   # Frontend
   docker-compose exec frontend sh
   
   # Database
   docker-compose exec postgres bash
   ```

2. **View Container Logs**
   ```bash
   # Real-time logs
   docker-compose logs -f backend
   
   # Last 100 lines
   docker-compose logs --tail=100 backend
   ```

## 📊 Performance Monitoring

### Backend Performance

1. **Enable Profiling**
   ```python
   # Add to main.py
   from fastapi import Request
   import time
   
   @app.middleware("http")
   async def add_process_time_header(request: Request, call_next):
       start_time = time.time()
       response = await call_next(request)
       process_time = time.time() - start_time
       response.headers["X-Process-Time"] = str(process_time)
       return response
   ```

2. **Database Query Monitoring**
   ```python
   # Enable SQLAlchemy logging
   import logging
   logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)
   ```

### Frontend Performance

1. **Bundle Analysis**
   ```bash
   cd frontend
   npm run build
   npm install -g webpack-bundle-analyzer
   webpack-bundle-analyzer dist/static/js/*.js
   ```

2. **Lighthouse Audits**: Use Chrome DevTools

## 🔄 CI/CD Pipeline

### GitHub Actions Example

```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install backend dependencies
      run: |
        cd backend
        pip install -r requirements.txt
    
    - name: Run backend tests
      run: |
        cd backend
        pytest
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Run frontend tests
      run: |
        cd frontend
        npm test
    
    - name: Build application
      run: |
        docker-compose build
```

## 🚀 Deployment

### Production Checklist

- [ ] Update all environment variables for production
- [ ] Generate new JWT secret key
- [ ] Configure SSL certificates
- [ ] Set up production database
- [ ] Configure AWS S3 production bucket
- [ ] Set up monitoring and logging
- [ ] Configure backup strategy
- [ ] Test all functionality in staging environment

### Docker Production

1. **Create Production Compose File**
   ```yaml
   # docker-compose.prod.yml
   version: '3.8'
   services:
     backend:
       environment:
         - APP_ENV=production
         - DEBUG=false
     frontend:
       command: npm run build && npm run preview
   ```

2. **Deploy**
   ```bash
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
   ```

## 🤝 Contributing

### Code Style

1. **Backend (Python)**
   - Use Black for formatting
   - Use isort for import sorting
   - Follow PEP 8 guidelines
   - Use type hints

2. **Frontend (TypeScript)**
   - Use Prettier for formatting
   - Follow ESLint rules
   - Use TypeScript strict mode
   - Follow React best practices

### Git Workflow

1. **Branch Naming**
   - `feature/description`
   - `bugfix/description`
   - `hotfix/description`

2. **Commit Messages**
   ```
   type(scope): description
   
   feat(auth): add JWT token refresh
   fix(upload): handle large file uploads
   docs(readme): update installation guide
   ```

### Pull Request Process

1. Create feature branch from `develop`
2. Make changes with tests
3. Update documentation
4. Create pull request to `develop`
5. Code review and approval
6. Merge to `develop`
7. Deploy to staging for testing
8. Merge to `main` for production

## 📚 Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://react.dev/)
- [Docker Documentation](https://docs.docker.com/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Redis Documentation](https://redis.io/documentation)
- [AWS S3 Documentation](https://docs.aws.amazon.com/s3/)

## 🆘 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Find process using port
   lsof -i :8000
   
   # Kill process
   kill -9 <PID>
   ```

2. **Database Connection Issues**
   ```bash
   # Check if PostgreSQL is running
   docker-compose ps postgres
   
   # Restart PostgreSQL
   docker-compose restart postgres
   ```

3. **S3 Upload Issues**
   - Verify AWS credentials
   - Check bucket permissions
   - Ensure bucket exists in correct region

4. **Frontend Build Issues**
   ```bash
   # Clear node_modules and reinstall
   cd frontend
   rm -rf node_modules package-lock.json
   npm install
   ```

5. **Docker Issues**
   ```bash
   # Clean up Docker
   docker system prune -a
   
   # Rebuild without cache
   docker-compose build --no-cache
   ```

For more help, check the logs or open an issue on GitHub.