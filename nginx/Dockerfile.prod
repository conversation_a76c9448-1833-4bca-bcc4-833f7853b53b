# Production Dockerfile for ShareX Nginx
FROM nginx:alpine

# Install security updates
RUN apk update && apk upgrade && apk add --no-cache \
    curl \
    && rm -rf /var/cache/apk/*

# Remove default nginx configuration
RUN rm /etc/nginx/conf.d/default.conf

# Copy production nginx configuration
COPY nginx.prod.conf /etc/nginx/nginx.conf

# Create nginx user and set permissions
RUN addgroup -g 1001 -S nginx_user \
    && adduser -S nginx_user -u 1001 -G nginx_user \
    && chown -R nginx_user:nginx_user /var/cache/nginx \
    && chown -R nginx_user:nginx_user /var/log/nginx \
    && chown -R nginx_user:nginx_user /etc/nginx \
    && touch /var/run/nginx.pid \
    && chown -R nginx_user:nginx_user /var/run/nginx.pid

# Create directory for SSL certificates
RUN mkdir -p /etc/nginx/ssl \
    && chown -R nginx_user:nginx_user /etc/nginx/ssl

# Switch to non-root user
USER nginx_user

# Expose ports
EXPOSE 80 443

# Health check
HEALTHCHECK --interval=60s --timeout=30s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
