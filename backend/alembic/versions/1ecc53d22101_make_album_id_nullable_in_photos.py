"""make_album_id_nullable_in_photos

Revision ID: 1ecc53d22101
Revises: d1b05e681535
Create Date: 2025-09-27 05:53:28.056430

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1ecc53d22101'
down_revision: Union[str, None] = 'd1b05e681535'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('photos', 'album_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('photos', 'album_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    # ### end Alembic commands ###
