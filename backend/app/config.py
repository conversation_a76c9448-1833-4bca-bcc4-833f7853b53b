from pydantic_settings import BaseSettings
from typing import List
import os

class Settings(BaseSettings):
    # Database
    database_url: str = os.getenv("DATABASE_URL", "******************************************************/sharex_db")
    
    # Redis
    redis_url: str = os.getenv("REDIS_URL", "redis://redis:6379/0")
    
    # JWT
    jwt_secret_key: str = os.getenv("JWT_SECRET_KEY", "your_super_secret_jwt_key_change_this_in_production")
    jwt_algorithm: str = os.getenv("JWT_ALGORITHM", "HS256")
    jwt_access_token_expire_minutes: int = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "60"))
    
    # AWS S3
    aws_access_key_id: str = os.getenv("AWS_ACCESS_KEY_ID", "")
    aws_secret_access_key: str = os.getenv("AWS_SECRET_ACCESS_KEY", "")
    aws_s3_bucket_name: str = os.getenv("AWS_S3_BUCKET_NAME", "")
    aws_region: str = os.getenv("AWS_REGION", "us-east-1")
    
    # Application
    app_env: str = os.getenv("APP_ENV", "development")
    debug: bool = os.getenv("DEBUG", "true").lower() == "true"
    
    @property
    def allowed_origins(self) -> List[str]:
        origins = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000")
        return [origin.strip() for origin in origins.split(",")]
    
    # File Upload
    max_file_size: int = int(os.getenv("MAX_FILE_SIZE", "10485760"))  # 10MB
    
    @property
    def allowed_file_types(self) -> List[str]:
        types = os.getenv("ALLOWED_FILE_TYPES", "image/jpeg,image/png,image/webp")
        return [file_type.strip() for file_type in types.split(",")]
    
    # Admin defaults
    default_admin_email: str = os.getenv("DEFAULT_ADMIN_EMAIL", "<EMAIL>")
    default_admin_password: str = os.getenv("DEFAULT_ADMIN_PASSWORD", "admin123")
    
    class Config:
        env_file = ".env"

# Global settings instance
settings = Settings()