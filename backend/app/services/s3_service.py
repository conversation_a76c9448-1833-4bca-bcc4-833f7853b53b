import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from PIL import Image
import io
import uuid
import logging
from typing import Optional, <PERSON>ple
from app.config import settings

logger = logging.getLogger(__name__)

class S3Service:
    def __init__(self):
        try:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=settings.aws_access_key_id,
                aws_secret_access_key=settings.aws_secret_access_key,
                region_name=settings.aws_region
            )
            self.bucket_name = settings.aws_s3_bucket_name
            
            # Test connection
            self._test_connection()
            
        except NoCredentialsError:
            logger.error("AWS credentials not found")
            raise
        except Exception as e:
            logger.error(f"Error initializing S3 service: {str(e)}")
            raise

    def _test_connection(self):
        """Test S3 connection and bucket access"""
        try:
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            logger.info(f"Successfully connected to S3 bucket: {self.bucket_name}")
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == '404':
                logger.error(f"S3 bucket '{self.bucket_name}' not found")
            elif error_code == '403':
                logger.error(f"Access denied to S3 bucket '{self.bucket_name}'")
            else:
                logger.error(f"Error accessing S3 bucket: {str(e)}")
            raise

    def upload_photo(self, file_content: bytes, filename: str, content_type: str) -> Tuple[str, str]:
        """
        Upload photo to S3 and generate thumbnail
        Returns: (original_key, thumbnail_key)
        """
        try:
            # Generate unique filename
            file_extension = filename.split('.')[-1].lower()
            unique_filename = f"{uuid.uuid4()}.{file_extension}"
            
            # Upload original photo
            original_key = f"photos/original/{unique_filename}"
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=original_key,
                Body=file_content,
                ContentType=content_type,
                Metadata={
                    'original_filename': filename,
                    'upload_type': 'original'
                }
            )
            
            # Generate and upload thumbnail
            thumbnail_key = self._generate_and_upload_thumbnail(
                file_content, unique_filename, content_type
            )
            
            logger.info(f"Successfully uploaded photo: {original_key}")
            return original_key, thumbnail_key
            
        except Exception as e:
            logger.error(f"Error uploading photo: {str(e)}")
            raise

    def _generate_and_upload_thumbnail(self, file_content: bytes, filename: str, content_type: str) -> str:
        """Generate thumbnail and upload to S3"""
        try:
            # Open image with PIL
            image = Image.open(io.BytesIO(file_content))
            
            # Convert to RGB if necessary (for PNG with transparency)
            if image.mode in ('RGBA', 'LA', 'P'):
                # Create white background
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = background
            
            # Generate thumbnail (400x400 max, maintain aspect ratio)
            image.thumbnail((400, 400), Image.Resampling.LANCZOS)
            
            # Save thumbnail to bytes
            thumbnail_buffer = io.BytesIO()
            image.save(thumbnail_buffer, format='JPEG', quality=85, optimize=True)
            thumbnail_content = thumbnail_buffer.getvalue()
            
            # Upload thumbnail
            thumbnail_key = f"photos/thumbnails/{filename}"
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=thumbnail_key,
                Body=thumbnail_content,
                ContentType='image/jpeg',
                Metadata={
                    'upload_type': 'thumbnail',
                    'original_filename': filename
                }
            )
            
            logger.info(f"Successfully generated thumbnail: {thumbnail_key}")
            return thumbnail_key
            
        except Exception as e:
            logger.error(f"Error generating thumbnail: {str(e)}")
            # Return None if thumbnail generation fails (non-critical)
            return None

    def delete_photo(self, original_key: str, thumbnail_key: Optional[str] = None):
        """Delete photo and thumbnail from S3"""
        try:
            # Delete original
            self.s3_client.delete_object(Bucket=self.bucket_name, Key=original_key)
            logger.info(f"Deleted original photo: {original_key}")
            
            # Delete thumbnail if exists
            if thumbnail_key:
                self.s3_client.delete_object(Bucket=self.bucket_name, Key=thumbnail_key)
                logger.info(f"Deleted thumbnail: {thumbnail_key}")
                
        except Exception as e:
            logger.error(f"Error deleting photo from S3: {str(e)}")
            raise

    def generate_presigned_url(self, key: str, expiration: int = 3600) -> str:
        """Generate presigned URL for photo access"""
        try:
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.bucket_name, 'Key': key},
                ExpiresIn=expiration
            )
            return url
        except Exception as e:
            logger.error(f"Error generating presigned URL: {str(e)}")
            raise

    def get_photo_info(self, key: str) -> dict:
        """Get photo metadata from S3"""
        try:
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=key)
            return {
                'size': response['ContentLength'],
                'last_modified': response['LastModified'],
                'content_type': response['ContentType'],
                'metadata': response.get('Metadata', {})
            }
        except Exception as e:
            logger.error(f"Error getting photo info: {str(e)}")
            raise

# Global S3 service instance
s3_service = S3Service()