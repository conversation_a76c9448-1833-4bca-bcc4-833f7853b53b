from sqlalchemy import <PERSON><PERSON>n, Inte<PERSON>, String, Text, DateTime, Foreign<PERSON>ey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base

class Message(Base):
    __tablename__ = "messages"

    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text, nullable=False)
    sender_name = Column(String, nullable=True)  # For anonymous users
    is_from_user = Column(Boolean, default=True)  # True if from user, False if from moderator
    is_read = Column(Boolean, default=False)
    
    # Foreign Keys
    shared_link_id = Column(Integer, ForeignKey("shared_links.id"), nullable=False)
    sender_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # Null for anonymous users
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    read_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    shared_link = relationship("SharedLink", back_populates="messages")
    sender = relationship("User", back_populates="messages")
    
    def __repr__(self):
        sender_info = f"user_id={self.sender_id}" if self.sender_id else f"anonymous='{self.sender_name}'"
        return f"<Message(id={self.id}, {sender_info}, shared_link_id={self.shared_link_id})>"
    
    @property
    def sender_display_name(self) -> str:
        """Return the display name for the sender"""
        if self.sender:
            return self.sender.full_name or self.sender.email
        return self.sender_name or "Anonymous User"
    
    @property
    def is_from_moderator(self) -> bool:
        """Check if message is from a moderator"""
        return not self.is_from_user
    
    def mark_as_read(self):
        """Mark the message as read"""
        if not self.is_read:
            self.is_read = True
            self.read_at = func.now()