from sqlalchemy import Column, Integer, String, Text, DateTime, Foreign<PERSON>ey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base

class Album(Base):
    __tablename__ = "albums"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    description = Column(Text, nullable=True)
    cover_photo_id = Column(Integer, nullable=True)  # Will be set after photos table exists
    is_active = Column(Boolean, default=True)
    
    # Foreign Keys
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    owner = relationship("User", back_populates="albums")
    photos = relationship("Photo", back_populates="album", cascade="all, delete-orphan")
    shared_links = relationship("SharedLink", back_populates="album", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Album(id={self.id}, name='{self.name}', owner_id={self.owner_id})>"
    
    @property
    def photo_count(self) -> int:
        return len(self.photos) if self.photos else 0
    
    @property
    def is_empty(self) -> bool:
        return self.photo_count == 0