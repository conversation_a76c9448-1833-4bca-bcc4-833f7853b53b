from sqlalchemy import Column, Integer, String, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import secrets
import hashlib
from datetime import datetime, timedelta

class SharedLink(Base):
    __tablename__ = "shared_links"

    id = Column(Integer, primary_key=True, index=True)
    access_code = Column(String(6), nullable=False, index=True)  # 6-digit code
    access_code_hash = Column(String, nullable=False)  # Hashed version for security
    link_token = Column(String, unique=True, nullable=False, index=True)  # Unique link identifier
    is_active = Column(Boolean, default=True)
    access_count = Column(Integer, default=0)  # Track how many times accessed
    max_access_count = Column(Integer, nullable=True)  # Optional access limit
    
    # Foreign Keys (either album_id OR photo_id, not both)
    album_id = Column(Integer, ForeignKey("albums.id"), nullable=True)
    photo_id = Column(Integer, <PERSON><PERSON><PERSON>("photos.id"), nullable=True)
    creator_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=False)
    last_accessed = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    album = relationship("Album", back_populates="shared_links")
    photo = relationship("Photo", back_populates="shared_links")
    creator = relationship("User", back_populates="shared_links")
    messages = relationship("Message", back_populates="shared_link", cascade="all, delete-orphan")
    
    def __repr__(self):
        content_type = "album" if self.album_id else "photo"
        content_id = self.album_id or self.photo_id
        return f"<SharedLink(id={self.id}, {content_type}_id={content_id}, expires_at={self.expires_at})>"
    
    @classmethod
    def generate_access_code(cls) -> str:
        """Generate a 6-digit access code"""
        return f"{secrets.randbelow(1000000):06d}"
    
    @classmethod
    def hash_access_code(cls, code: str) -> str:
        """Hash the access code for secure storage"""
        return hashlib.sha256(code.encode()).hexdigest()
    
    @classmethod
    def generate_link_token(cls) -> str:
        """Generate a unique link token"""
        return secrets.token_urlsafe(32)
    
    def verify_access_code(self, code: str) -> bool:
        """Verify if the provided code matches the stored hash"""
        return self.access_code_hash == self.hash_access_code(code)
    
    @property
    def is_expired(self) -> bool:
        """Check if the link has expired"""
        return datetime.utcnow() > self.expires_at
    
    @property
    def is_valid(self) -> bool:
        """Check if the link is valid (active and not expired)"""
        return self.is_active and not self.is_expired
    
    @property
    def content_type(self) -> str:
        """Return the type of content being shared"""
        return "album" if self.album_id else "photo"
    
    @property
    def content_id(self) -> int:
        """Return the ID of the content being shared"""
        return self.album_id or self.photo_id
    
    @property
    def time_until_expiry(self) -> timedelta:
        """Return time remaining until expiry"""
        return self.expires_at - datetime.utcnow()
    
    def increment_access_count(self):
        """Increment the access count and update last accessed time"""
        self.access_count += 1
        self.last_accessed = datetime.utcnow()