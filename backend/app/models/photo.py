from sqlalchemy import Column, Integer, String, Text, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON>an, BigInteger
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base

class Photo(Base):
    __tablename__ = "photos"

    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String, nullable=False)
    original_filename = Column(String, nullable=False)
    file_path = Column(String, nullable=False)  # S3 key for original
    thumbnail_path = Column(String, nullable=True)  # S3 key for thumbnail
    file_size = Column(BigInteger, nullable=False)  # Size in bytes
    mime_type = Column(String, nullable=False)
    width = Column(Integer, nullable=True)
    height = Column(Integer, nullable=True)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    
    # Foreign Keys
    album_id = Column(Integer, ForeignKey("albums.id"), nullable=False)
    uploaded_by = Column(Integer, <PERSON><PERSON><PERSON>("users.id"), nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    album = relationship("Album", back_populates="photos")
    uploader = relationship("User")
    shared_links = relationship("SharedLink", back_populates="photo", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Photo(id={self.id}, filename='{self.filename}', album_id={self.album_id})>"
    
    @property
    def file_size_mb(self) -> float:
        """Return file size in MB"""
        return round(self.file_size / (1024 * 1024), 2)
    
    @property
    def has_thumbnail(self) -> bool:
        return self.thumbnail_path is not None
    
    @property
    def aspect_ratio(self) -> float:
        """Return aspect ratio (width/height)"""
        if self.width and self.height:
            return self.width / self.height
        return 1.0