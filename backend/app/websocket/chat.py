import socketio
from app.database import SessionLocal
from app.models.message import Message
from app.models.shared_link import SharedLink
from app.models.user import User
import logging

logger = logging.getLogger(__name__)

# Create Socket.IO server
sio = socketio.AsyncServer(
    cors_allowed_origins="*",
    logger=True,
    engineio_logger=True
)

# Store active connections
active_connections = {}

@sio.event
async def connect(sid, environ, auth):
    """Handle client connection"""
    logger.info(f"Client {sid} connected")
    
    # Extract connection info from auth
    shared_link_token = auth.get('shared_link_token') if auth else None
    user_id = auth.get('user_id') if auth else None
    user_name = auth.get('user_name', 'Anonymous') if auth else 'Anonymous'
    
    # Store connection info
    active_connections[sid] = {
        'shared_link_token': shared_link_token,
        'user_id': user_id,
        'user_name': user_name,
        'is_moderator': user_id is not None
    }
    
    # Join room based on shared link
    if shared_link_token:
        await sio.enter_room(sid, f"link_{shared_link_token}")
        logger.info(f"Client {sid} joined room link_{shared_link_token}")

@sio.event
async def disconnect(sid):
    """Handle client disconnection"""
    logger.info(f"Client {sid} disconnected")
    
    # Clean up connection info
    if sid in active_connections:
        del active_connections[sid]

@sio.event
async def send_message(sid, data):
    """Handle sending a message"""
    try:
        connection_info = active_connections.get(sid)
        if not connection_info:
            await sio.emit('error', {'message': 'Connection not found'}, room=sid)
            return
        
        shared_link_token = connection_info['shared_link_token']
        user_id = connection_info['user_id']
        user_name = connection_info['user_name']
        is_moderator = connection_info['is_moderator']
        
        message_content = data.get('message', '').strip()
        if not message_content:
            await sio.emit('error', {'message': 'Message cannot be empty'}, room=sid)
            return
        
        # Get shared link
        db = SessionLocal()
        try:
            shared_link = db.query(SharedLink).filter(
                SharedLink.link_token == shared_link_token
            ).first()
            
            if not shared_link:
                await sio.emit('error', {'message': 'Invalid shared link'}, room=sid)
                return
            
            # Create message record
            message = Message(
                content=message_content,
                sender_name=user_name if not is_moderator else None,
                is_from_user=not is_moderator,
                shared_link_id=shared_link.id,
                sender_id=user_id if is_moderator else None
            )
            
            db.add(message)
            db.commit()
            db.refresh(message)
            
            # Broadcast message to all clients in the room
            message_data = {
                'id': message.id,
                'content': message.content,
                'sender_name': message.sender_display_name,
                'is_from_user': message.is_from_user,
                'created_at': message.created_at.isoformat(),
                'sender_id': user_id
            }
            
            await sio.emit('new_message', message_data, room=f"link_{shared_link_token}")
            logger.info(f"Message sent in room link_{shared_link_token}")
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error sending message: {str(e)}")
        await sio.emit('error', {'message': 'Failed to send message'}, room=sid)

@sio.event
async def get_messages(sid, data):
    """Get message history for a shared link"""
    try:
        connection_info = active_connections.get(sid)
        if not connection_info:
            await sio.emit('error', {'message': 'Connection not found'}, room=sid)
            return
        
        shared_link_token = connection_info['shared_link_token']
        
        db = SessionLocal()
        try:
            shared_link = db.query(SharedLink).filter(
                SharedLink.link_token == shared_link_token
            ).first()
            
            if not shared_link:
                await sio.emit('error', {'message': 'Invalid shared link'}, room=sid)
                return
            
            # Get messages for this shared link
            messages = db.query(Message).filter(
                Message.shared_link_id == shared_link.id
            ).order_by(Message.created_at.asc()).all()
            
            message_list = [
                {
                    'id': msg.id,
                    'content': msg.content,
                    'sender_name': msg.sender_display_name,
                    'is_from_user': msg.is_from_user,
                    'created_at': msg.created_at.isoformat(),
                    'sender_id': msg.sender_id
                }
                for msg in messages
            ]
            
            await sio.emit('message_history', {'messages': message_list}, room=sid)
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error getting messages: {str(e)}")
        await sio.emit('error', {'message': 'Failed to get messages'}, room=sid)

@sio.event
async def typing_start(sid, data):
    """Handle typing indicator start"""
    connection_info = active_connections.get(sid)
    if connection_info and connection_info['shared_link_token']:
        room = f"link_{connection_info['shared_link_token']}"
        await sio.emit('user_typing', {
            'user_name': connection_info['user_name'],
            'is_moderator': connection_info['is_moderator']
        }, room=room, skip_sid=sid)

@sio.event
async def typing_stop(sid, data):
    """Handle typing indicator stop"""
    connection_info = active_connections.get(sid)
    if connection_info and connection_info['shared_link_token']:
        room = f"link_{connection_info['shared_link_token']}"
        await sio.emit('user_stopped_typing', {
            'user_name': connection_info['user_name'],
            'is_moderator': connection_info['is_moderator']
        }, room=room, skip_sid=sid)

# Export the Socket.IO app
socket_app = socketio.ASGIApp(sio, other_asgi_app=None)