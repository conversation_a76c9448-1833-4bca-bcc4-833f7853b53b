from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
from app.database import get_db
from app.models.user import User
from app.models.album import Album
from app.models.photo import Photo
from app.models.shared_link import SharedLink
from app.utils.dependencies import get_current_moderator
from app.services.s3_service import s3_service
from app.config import settings
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/moderator", tags=["Moderator"])

@router.post("/photos/upload")
async def upload_photos(
    files: List[UploadFile] = File(...),
    album_id: Optional[int] = Form(None),
    current_user: User = Depends(get_current_moderator),
    db: Session = Depends(get_db)
):
    """Upload photos to S3 and save metadata to database"""
    uploaded_photos = []
    
    # Validate album if provided
    album = None
    if album_id:
        album = db.query(Album).filter(
            Album.id == album_id,
            Album.owner_id == current_user.id
        ).first()
        if not album:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Album not found or access denied",
            )
    
    for file in files:
        try:
            # Validate file type
            if file.content_type not in settings.allowed_file_types:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"File type {file.content_type} not allowed",
                )
            
            # Validate file size
            file_content = await file.read()
            if len(file_content) > settings.max_file_size:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"File size exceeds maximum allowed size",
                )
            
            # Get image dimensions
            try:
                image = Image.open(io.BytesIO(file_content))
                width, height = image.size
            except Exception:
                width = height = None
            
            # Upload to S3
            original_key, thumbnail_key = s3_service.upload_photo(
                file_content, file.filename, file.content_type
            )
            
            # Create photo record
            photo = Photo(
                filename=f"{uuid.uuid4()}.{file.filename.split('.')[-1]}",
                original_filename=file.filename,
                file_path=original_key,
                thumbnail_path=thumbnail_key,
                file_size=len(file_content),
                mime_type=file.content_type,
                width=width,
                height=height,
                album_id=album_id,
                uploaded_by=current_user.id
            )
            
            db.add(photo)
            db.commit()
            db.refresh(photo)
            
            uploaded_photos.append({
                "id": photo.id,
                "filename": photo.filename,
                "original_filename": photo.original_filename,
                "file_size": photo.file_size,
                "mime_type": photo.mime_type,
                "width": photo.width,
                "height": photo.height,
                "thumbnail_url": s3_service.generate_presigned_url(thumbnail_key) if thumbnail_key else None,
                "created_at": photo.created_at,
            })
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error uploading photo {file.filename}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error uploading {file.filename}",
            )
    
    return {
        "message": f"Successfully uploaded {len(uploaded_photos)} photos",
        "photos": uploaded_photos
    }

@router.get("/photos")
async def get_photos(
    album_id: Optional[int] = None,
    current_user: User = Depends(get_current_moderator),
    db: Session = Depends(get_db)
):
    """Get moderator's photos"""
    query = db.query(Photo).filter(Photo.uploaded_by == current_user.id)
    
    if album_id:
        # Verify album ownership
        album = db.query(Album).filter(
            Album.id == album_id,
            Album.owner_id == current_user.id
        ).first()
        if not album:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Album not found or access denied",
            )
        query = query.filter(Photo.album_id == album_id)
    
    photos = query.order_by(Photo.created_at.desc()).all()
    
    return [
        {
            "id": photo.id,
            "filename": photo.filename,
            "original_filename": photo.original_filename,
            "file_size": photo.file_size,
            "mime_type": photo.mime_type,
            "width": photo.width,
            "height": photo.height,
            "description": photo.description,
            "album_id": photo.album_id,
            "thumbnail_url": s3_service.generate_presigned_url(photo.thumbnail_path) if photo.thumbnail_path else None,
            "photo_url": s3_service.generate_presigned_url(photo.file_path),
            "created_at": photo.created_at,
        }
        for photo in photos
    ]

@router.delete("/photos/{photo_id}")
async def delete_photo(
    photo_id: int,
    current_user: User = Depends(get_current_moderator),
    db: Session = Depends(get_db)
):
    """Delete a photo"""
    photo = db.query(Photo).filter(
        Photo.id == photo_id,
        Photo.uploaded_by == current_user.id
    ).first()
    
    if not photo:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Photo not found or access denied",
        )
    
    try:
        # Delete from S3
        s3_service.delete_photo(photo.file_path, photo.thumbnail_path)
        
        # Delete from database
        db.delete(photo)
        db.commit()
        
        return {"message": "Photo deleted successfully"}
        
    except Exception as e:
        logger.error(f"Error deleting photo: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting photo",
        )

@router.get("/albums")
async def get_albums(
    current_user: User = Depends(get_current_moderator),
    db: Session = Depends(get_db)
):
    """Get moderator's albums"""
    albums = db.query(Album).filter(Album.owner_id == current_user.id).all()
    
    return [
        {
            "id": album.id,
            "name": album.name,
            "description": album.description,
            "photo_count": len(album.photos),
            "cover_photo_id": album.cover_photo_id,
            "created_at": album.created_at,
            "updated_at": album.updated_at,
        }
        for album in albums
    ]

@router.post("/albums")
async def create_album(
    name: str = Form(...),
    description: Optional[str] = Form(None),
    current_user: User = Depends(get_current_moderator),
    db: Session = Depends(get_db)
):
    """Create a new album"""
    album = Album(
        name=name,
        description=description,
        owner_id=current_user.id
    )
    
    db.add(album)
    db.commit()
    db.refresh(album)
    
    return {
        "id": album.id,
        "name": album.name,
        "description": album.description,
        "photo_count": 0,
        "created_at": album.created_at,
    }

@router.put("/albums/{album_id}")
async def update_album(
    album_id: int,
    name: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    cover_photo_id: Optional[int] = Form(None),
    current_user: User = Depends(get_current_moderator),
    db: Session = Depends(get_db)
):
    """Update album details"""
    album = db.query(Album).filter(
        Album.id == album_id,
        Album.owner_id == current_user.id
    ).first()
    
    if not album:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Album not found or access denied",
        )
    
    if name is not None:
        album.name = name
    if description is not None:
        album.description = description
    if cover_photo_id is not None:
        # Verify photo belongs to this album
        photo = db.query(Photo).filter(
            Photo.id == cover_photo_id,
            Photo.album_id == album_id
        ).first()
        if photo:
            album.cover_photo_id = cover_photo_id
    
    db.commit()
    db.refresh(album)
    
    return {
        "id": album.id,
        "name": album.name,
        "description": album.description,
        "photo_count": len(album.photos),
        "cover_photo_id": album.cover_photo_id,
        "updated_at": album.updated_at,
    }

@router.delete("/albums/{album_id}")
async def delete_album(
    album_id: int,
    current_user: User = Depends(get_current_moderator),
    db: Session = Depends(get_db)
):
    """Delete an album and all its photos"""
    album = db.query(Album).filter(
        Album.id == album_id,
        Album.owner_id == current_user.id
    ).first()
    
    if not album:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Album not found or access denied",
        )
    
    try:
        # Delete all photos from S3
        for photo in album.photos:
            s3_service.delete_photo(photo.file_path, photo.thumbnail_path)
        
        # Delete album (cascade will delete photos from DB)
        db.delete(album)
        db.commit()
        
        return {"message": "Album and all photos deleted successfully"}
        
    except Exception as e:
        logger.error(f"Error deleting album: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting album",
        )