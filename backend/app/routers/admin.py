from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.database import get_db
from app.models.user import User, UserRole
from app.models.album import Album
from app.models.photo import Photo
from app.models.shared_link import SharedLink
from app.schemas.user import UserCreate, UserResponse, UserUpdate
from app.utils.dependencies import get_current_admin
from app.utils.auth import get_password_hash
from sqlalchemy import func

router = APIRouter(prefix="/api/admin", tags=["Admin"])

@router.get("/moderators", response_model=List[UserResponse])
async def get_moderators(
    current_admin: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """Get all moderators"""
    moderators = db.query(User).filter(User.role == UserRole.MODERATOR).all()
    return moderators

@router.post("/moderators", response_model=UserResponse)
async def create_moderator(
    user_data: UserCreate,
    current_admin: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """Create a new moderator"""
    # Check if user already exists
    existing_user = db.query(User).filter(User.email == user_data.email).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered",
        )
    
    # Create new moderator
    hashed_password = get_password_hash(user_data.password)
    new_moderator = User(
        email=user_data.email,
        hashed_password=hashed_password,
        full_name=user_data.full_name,
        role=UserRole.MODERATOR
    )
    
    db.add(new_moderator)
    db.commit()
    db.refresh(new_moderator)
    
    return new_moderator

@router.get("/moderators/{moderator_id}", response_model=UserResponse)
async def get_moderator(
    moderator_id: int,
    current_admin: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """Get moderator details"""
    moderator = db.query(User).filter(
        User.id == moderator_id,
        User.role == UserRole.MODERATOR
    ).first()
    
    if not moderator:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Moderator not found",
        )
    
    return moderator

@router.put("/moderators/{moderator_id}", response_model=UserResponse)
async def update_moderator(
    moderator_id: int,
    user_data: UserUpdate,
    current_admin: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """Update moderator details"""
    moderator = db.query(User).filter(
        User.id == moderator_id,
        User.role == UserRole.MODERATOR
    ).first()
    
    if not moderator:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Moderator not found",
        )
    
    # Update fields
    if user_data.email is not None:
        # Check if email is already taken by another user
        existing_user = db.query(User).filter(
            User.email == user_data.email,
            User.id != moderator_id
        ).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already taken",
            )
        moderator.email = user_data.email
    
    if user_data.full_name is not None:
        moderator.full_name = user_data.full_name
    
    if user_data.is_active is not None:
        moderator.is_active = user_data.is_active
    
    db.commit()
    db.refresh(moderator)
    
    return moderator

@router.delete("/moderators/{moderator_id}")
async def delete_moderator(
    moderator_id: int,
    current_admin: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """Delete a moderator"""
    moderator = db.query(User).filter(
        User.id == moderator_id,
        User.role == UserRole.MODERATOR
    ).first()
    
    if not moderator:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Moderator not found",
        )
    
    db.delete(moderator)
    db.commit()
    
    return {"message": "Moderator deleted successfully"}

@router.get("/statistics")
async def get_statistics(
    current_admin: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """Get platform statistics"""
    # Count statistics
    total_moderators = db.query(User).filter(User.role == UserRole.MODERATOR).count()
    total_albums = db.query(Album).count()
    total_photos = db.query(Photo).count()
    active_links = db.query(SharedLink).filter(SharedLink.is_active == True).count()
    
    # Recent activity
    recent_moderators = db.query(User).filter(
        User.role == UserRole.MODERATOR
    ).order_by(User.created_at.desc()).limit(5).all()
    
    recent_albums = db.query(Album).order_by(Album.created_at.desc()).limit(5).all()
    
    return {
        "totals": {
            "moderators": total_moderators,
            "albums": total_albums,
            "photos": total_photos,
            "active_links": active_links,
        },
        "recent": {
            "moderators": [
                {
                    "id": mod.id,
                    "email": mod.email,
                    "full_name": mod.full_name,
                    "created_at": mod.created_at,
                }
                for mod in recent_moderators
            ],
            "albums": [
                {
                    "id": album.id,
                    "name": album.name,
                    "owner_email": album.owner.email,
                    "photo_count": len(album.photos),
                    "created_at": album.created_at,
                }
                for album in recent_albums
            ],
        }
    }

@router.get("/moderators/{moderator_id}/content")
async def get_moderator_content(
    moderator_id: int,
    current_admin: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """Get moderator's content (read-only for admin)"""
    moderator = db.query(User).filter(
        User.id == moderator_id,
        User.role == UserRole.MODERATOR
    ).first()
    
    if not moderator:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Moderator not found",
        )
    
    # Get moderator's albums with photo counts
    albums = db.query(Album).filter(Album.owner_id == moderator_id).all()
    
    # Get moderator's active shared links
    shared_links = db.query(SharedLink).filter(
        SharedLink.creator_id == moderator_id,
        SharedLink.is_active == True
    ).all()
    
    return {
        "moderator": {
            "id": moderator.id,
            "email": moderator.email,
            "full_name": moderator.full_name,
            "created_at": moderator.created_at,
        },
        "albums": [
            {
                "id": album.id,
                "name": album.name,
                "description": album.description,
                "photo_count": len(album.photos),
                "created_at": album.created_at,
            }
            for album in albums
        ],
        "shared_links": [
            {
                "id": link.id,
                "content_type": link.content_type,
                "content_id": link.content_id,
                "access_count": link.access_count,
                "expires_at": link.expires_at,
                "created_at": link.created_at,
            }
            for link in shared_links
        ],
    }