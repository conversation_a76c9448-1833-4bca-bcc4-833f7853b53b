from fastapi import APIRouter, Depends, HTTPException, status, Form
from sqlalchemy.orm import Session
from app.database import get_db
from app.models.shared_link import SharedLink
from app.models.album import Album
from app.models.photo import Photo
from app.services.s3_service import s3_service
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/access", tags=["Public Access"])

@router.post("/validate")
async def validate_access(
    link_token: str = Form(...),
    access_code: str = Form(...),
    db: Session = Depends(get_db)
):
    """Validate access code and return content token"""
    
    # Find shared link by token
    shared_link = db.query(SharedLink).filter(
        SharedLink.link_token == link_token
    ).first()
    
    if not shared_link:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invalid link",
        )
    
    # Check if link is valid
    if not shared_link.is_valid:
        if shared_link.is_expired:
            raise HTTPException(
                status_code=status.HTTP_410_GONE,
                detail="Link has expired",
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Link is no longer active",
            )
    
    # Verify access code
    if not shared_link.verify_access_code(access_code):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid access code",
        )
    
    # Update access count and last accessed
    shared_link.increment_access_count()
    db.commit()
    
    # Return content access token (same as link token for simplicity)
    return {
        "content_token": shared_link.link_token,
        "content_type": shared_link.content_type,
        "expires_at": shared_link.expires_at,
        "message": "Access granted"
    }

@router.get("/content/{content_token}")
async def get_shared_content(
    content_token: str,
    db: Session = Depends(get_db)
):
    """Get shared content using content token"""
    
    # Find shared link by token
    shared_link = db.query(SharedLink).filter(
        SharedLink.link_token == content_token
    ).first()
    
    if not shared_link:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invalid content token",
        )
    
    # Check if link is still valid
    if not shared_link.is_valid:
        raise HTTPException(
            status_code=status.HTTP_410_GONE,
            detail="Content access has expired",
        )
    
    # Get content based on type
    if shared_link.album_id:
        # Get album with photos
        album = db.query(Album).filter(Album.id == shared_link.album_id).first()
        if not album:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Album not found",
            )
        
        # Get photos with presigned URLs
        photos = []
        for photo in album.photos:
            photos.append({
                "id": photo.id,
                "filename": photo.original_filename,
                "description": photo.description,
                "width": photo.width,
                "height": photo.height,
                "file_size": photo.file_size,
                "thumbnail_url": s3_service.generate_presigned_url(photo.thumbnail_path) if photo.thumbnail_path else None,
                "photo_url": s3_service.generate_presigned_url(photo.file_path),
                "created_at": photo.created_at,
            })
        
        return {
            "type": "album",
            "id": album.id,
            "name": album.name,
            "description": album.description,
            "photos": photos,
            "photo_count": len(photos),
            "created_at": album.created_at,
            "owner": {
                "name": album.owner.full_name or album.owner.email,
                "email": album.owner.email,
            },
            "access_info": {
                "expires_at": shared_link.expires_at,
                "access_count": shared_link.access_count,
            }
        }
    
    else:
        # Get single photo
        photo = db.query(Photo).filter(Photo.id == shared_link.photo_id).first()
        if not photo:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Photo not found",
            )
        
        return {
            "type": "photo",
            "id": photo.id,
            "filename": photo.original_filename,
            "description": photo.description,
            "width": photo.width,
            "height": photo.height,
            "file_size": photo.file_size,
            "thumbnail_url": s3_service.generate_presigned_url(photo.thumbnail_path) if photo.thumbnail_path else None,
            "photo_url": s3_service.generate_presigned_url(photo.file_path),
            "created_at": photo.created_at,
            "album": {
                "id": photo.album.id,
                "name": photo.album.name,
            } if photo.album else None,
            "owner": {
                "name": photo.uploader.full_name or photo.uploader.email,
                "email": photo.uploader.email,
            },
            "access_info": {
                "expires_at": shared_link.expires_at,
                "access_count": shared_link.access_count,
            }
        }

@router.get("/link-info/{link_token}")
async def get_link_info(
    link_token: str,
    db: Session = Depends(get_db)
):
    """Get basic link information without requiring access code"""
    
    shared_link = db.query(SharedLink).filter(
        SharedLink.link_token == link_token
    ).first()
    
    if not shared_link:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invalid link",
        )
    
    # Get content name for display
    if shared_link.album_id:
        content = db.query(Album).filter(Album.id == shared_link.album_id).first()
        content_name = content.name if content else "Unknown Album"
    else:
        content = db.query(Photo).filter(Photo.id == shared_link.photo_id).first()
        content_name = content.original_filename if content else "Unknown Photo"
    
    return {
        "content_type": shared_link.content_type,
        "content_name": content_name,
        "is_expired": shared_link.is_expired,
        "is_active": shared_link.is_active,
        "expires_at": shared_link.expires_at,
        "owner": {
            "name": shared_link.creator.full_name or shared_link.creator.email,
        }
    }