from fastapi import APIRouter, Depends, HTTPException, status, Form
from sqlalchemy.orm import Session
from typing import Optional
from datetime import datetime, timedelta
from app.database import get_db
from app.models.user import User
from app.models.album import Album
from app.models.photo import Photo
from app.models.shared_link import SharedLink
from app.utils.dependencies import get_current_moderator
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/moderator/shared-links", tags=["Shared Links"])

@router.post("")
async def create_shared_link(
    content_type: str = Form(...),  # "album" or "photo"
    content_id: int = Form(...),
    expiry_hours: Optional[int] = Form(None),
    expiry_days: Optional[int] = Form(None),
    current_user: User = Depends(get_current_moderator),
    db: Session = Depends(get_db)
):
    """Create a secure sharing link with access code"""
    
    # Validate content type
    if content_type not in ["album", "photo"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Content type must be 'album' or 'photo'",
        )
    
    # Validate and get content
    if content_type == "album":
        content = db.query(Album).filter(
            Album.id == content_id,
            Album.owner_id == current_user.id
        ).first()
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Album not found or access denied",
            )
        album_id = content_id
        photo_id = None
    else:  # photo
        content = db.query(Photo).filter(
            Photo.id == content_id,
            Photo.uploaded_by == current_user.id
        ).first()
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Photo not found or access denied",
            )
        album_id = None
        photo_id = content_id
    
    # Calculate expiry time
    if expiry_days and expiry_hours:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot specify both expiry_days and expiry_hours",
        )
    
    if expiry_days:
        if expiry_days > 2:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Maximum expiry is 2 days",
            )
        expires_at = datetime.utcnow() + timedelta(days=expiry_days)
    elif expiry_hours:
        if expiry_hours > 48:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Maximum expiry is 48 hours (2 days)",
            )
        expires_at = datetime.utcnow() + timedelta(hours=expiry_hours)
    else:
        # Default to 24 hours
        expires_at = datetime.utcnow() + timedelta(hours=24)
    
    # Generate access code and link token
    access_code = SharedLink.generate_access_code()
    link_token = SharedLink.generate_link_token()
    
    # Create shared link
    shared_link = SharedLink(
        access_code=access_code,
        access_code_hash=SharedLink.hash_access_code(access_code),
        link_token=link_token,
        album_id=album_id,
        photo_id=photo_id,
        creator_id=current_user.id,
        expires_at=expires_at
    )
    
    db.add(shared_link)
    db.commit()
    db.refresh(shared_link)
    
    # Generate share URL
    share_url = f"http://localhost:3000/access?link={link_token}"
    
    return {
        "id": shared_link.id,
        "share_url": share_url,
        "access_code": access_code,  # Return plain code for display
        "content_type": content_type,
        "content_id": content_id,
        "content_name": content.name if hasattr(content, 'name') else content.original_filename,
        "expires_at": shared_link.expires_at,
        "created_at": shared_link.created_at,
    }

@router.get("")
async def get_shared_links(
    current_user: User = Depends(get_current_moderator),
    db: Session = Depends(get_db)
):
    """Get moderator's shared links"""
    shared_links = db.query(SharedLink).filter(
        SharedLink.creator_id == current_user.id
    ).order_by(SharedLink.created_at.desc()).all()
    
    result = []
    for link in shared_links:
        # Get content name
        if link.album_id:
            content = db.query(Album).filter(Album.id == link.album_id).first()
            content_name = content.name if content else "Unknown Album"
        else:
            content = db.query(Photo).filter(Photo.id == link.photo_id).first()
            content_name = content.original_filename if content else "Unknown Photo"
        
        # Generate share URL
        share_url = f"http://localhost:3000/access?link={link.link_token}"
        
        result.append({
            "id": link.id,
            "share_url": share_url,
            "access_code": link.access_code,  # Show original code
            "content_type": link.content_type,
            "content_id": link.content_id,
            "content_name": content_name,
            "is_active": link.is_active,
            "is_expired": link.is_expired,
            "access_count": link.access_count,
            "expires_at": link.expires_at,
            "created_at": link.created_at,
            "last_accessed": link.last_accessed,
        })
    
    return result

@router.delete("/{link_id}")
async def revoke_shared_link(
    link_id: int,
    current_user: User = Depends(get_current_moderator),
    db: Session = Depends(get_db)
):
    """Revoke (delete) a shared link"""
    shared_link = db.query(SharedLink).filter(
        SharedLink.id == link_id,
        SharedLink.creator_id == current_user.id
    ).first()
    
    if not shared_link:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Shared link not found or access denied",
        )
    
    db.delete(shared_link)
    db.commit()
    
    return {"message": "Shared link revoked successfully"}

@router.put("/{link_id}/deactivate")
async def deactivate_shared_link(
    link_id: int,
    current_user: User = Depends(get_current_moderator),
    db: Session = Depends(get_db)
):
    """Deactivate a shared link (soft delete)"""
    shared_link = db.query(SharedLink).filter(
        SharedLink.id == link_id,
        SharedLink.creator_id == current_user.id
    ).first()
    
    if not shared_link:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Shared link not found or access denied",
        )
    
    shared_link.is_active = False
    db.commit()
    
    return {"message": "Shared link deactivated successfully"}