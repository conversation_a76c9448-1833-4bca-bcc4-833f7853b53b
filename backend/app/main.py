from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.config import settings
from app.routers import auth, admin, moderator, shared_links, access
from app.websocket.chat import socket_app
import socketio

# Create FastAPI app
app = FastAPI(
    title="ShareX API",
    description="Modern photo sharing platform API",
    version="1.0.0",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router)
app.include_router(admin.router)
app.include_router(moderator.router)
app.include_router(shared_links.router)
app.include_router(access.router)

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "ShareX API is running!",
        "version": "1.0.0",
        "environment": settings.app_env
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "sharex-backend",
        "timestamp": "2024-01-01T00:00:00Z"
    }

# Mount Socket.IO app
app.mount("/socket.io", socket_app)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)