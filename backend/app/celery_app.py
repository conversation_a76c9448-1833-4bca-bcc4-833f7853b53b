from celery import Celery
import os

# Create Celery instance
celery_app = Celery(
    "sharex",
    broker=os.getenv("REDIS_URL", "redis://redis:6379/0"),
    backend=os.getenv("REDIS_URL", "redis://redis:6379/0"),
    include=["app.tasks"]
)

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_serializer_kwargs={
        'ensure_ascii': False,
    },
    result_expires=3600,
    worker_prefetch_multiplier=1,
    task_acks_late=True,
)

# Periodic tasks configuration
celery_app.conf.beat_schedule = {
    'check-expired-links': {
        'task': 'app.tasks.check_expired_links',
        'schedule': 300.0,  # Run every 5 minutes
    },
    'cleanup-old-data': {
        'task': 'app.tasks.cleanup_old_data',
        'schedule': 86400.0,  # Run daily
    },
}

if __name__ == "__main__":
    celery_app.start()