from sqlalchemy.orm import Session
from app.database import Session<PERSON>ocal
from app.models.user import User, UserRole
from app.utils.auth import get_password_hash
from app.config import settings
import logging

logger = logging.getLogger(__name__)

def create_admin_user():
    """Create the default admin user if it doesn't exist"""
    db = SessionLocal()
    try:
        # Check if admin already exists
        admin = db.query(User).filter(
            User.email == settings.default_admin_email,
            User.role == UserRole.ADMIN
        ).first()
        
        if admin:
            logger.info(f"Admin user already exists: {settings.default_admin_email}")
            return admin
        
        # Create admin user
        password = settings.default_admin_password
        logger.info(f"Using admin password: '{password}' (length: {len(password)})")
        
        if len(password) > 72:
            logger.warning(f"Password too long ({len(password)} chars), truncating to 72 chars")
            password = password[:72]
        
        hashed_password = get_password_hash(password)
        admin = User(
            email=settings.default_admin_email,
            hashed_password=hashed_password,
            full_name="System Administrator",
            role=UserRole.ADMIN,
            is_active=True
        )
        
        db.add(admin)
        db.commit()
        db.refresh(admin)
        
        logger.info(f"Admin user created successfully: {settings.default_admin_email}")
        logger.warning(f"Default password: {settings.default_admin_password} - CHANGE THIS IMMEDIATELY!")
        
        return admin
        
    except Exception as e:
        logger.error(f"Error creating admin user: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def seed_test_data():
    """Create test data for development"""
    db = SessionLocal()
    try:
        # Create test moderator
        test_moderator = db.query(User).filter(
            User.email == "<EMAIL>"
        ).first()
        
        if not test_moderator:
            hashed_password = get_password_hash("moderator123")
            test_moderator = User(
                email="<EMAIL>",
                hashed_password=hashed_password,
                full_name="Test Moderator",
                role=UserRole.MODERATOR,
                is_active=True
            )
            
            db.add(test_moderator)
            db.commit()
            db.refresh(test_moderator)
            
            logger.info("Test moderator created: <EMAIL> / moderator123")
        
        logger.info("Test data seeding completed")
        
    except Exception as e:
        logger.error(f"Error seeding test data: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    create_admin_user()
    seed_test_data()