# =============================================================================
# ShareX Application Configuration
# =============================================================================
# Copy this file to .env and update the values for your environment
# IMPORTANT: Never commit .env file to version control!

# =============================================================================
# Database Configuration
# =============================================================================
# PostgreSQL database connection settings
# Format: postgresql://username:password@host:port/database_name
DATABASE_URL=******************************************************/sharex_db

# PostgreSQL container environment variables
POSTGRES_USER=sharex_user
POSTGRES_PASSWORD=secure_password  # CHANGE THIS IN PRODUCTION!
POSTGRES_DB=sharex_db

# =============================================================================
# Redis Configuration
# =============================================================================
# Redis connection URL for caching and Celery broker
# Format: redis://host:port/database_number
REDIS_URL=redis://redis:6379/0

# =============================================================================
# AWS S3 Configuration
# =============================================================================
# AWS credentials for S3 photo storage
# REQUIRED: Get these from your AWS IAM user
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key_here

# S3 bucket name for storing photos and thumbnails
# REQUIRED: Create this bucket in your AWS account
AWS_S3_BUCKET_NAME=your-sharex-bucket-name

# AWS region where your S3 bucket is located
# Example: us-east-1, eu-west-1, ap-south-1
AWS_REGION=us-east-1

# =============================================================================
# JWT Authentication Configuration
# =============================================================================
# Secret key for signing JWT tokens
# IMPORTANT: Generate a new secure key for production!
# You can generate one with: openssl rand -hex 32
JWT_SECRET_KEY=your_super_secret_jwt_key_change_this_in_production

# JWT algorithm (recommended: HS256)
JWT_ALGORITHM=HS256

# JWT token expiration time in minutes
# Default: 60 minutes (1 hour)
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=60

# =============================================================================
# Application Configuration
# =============================================================================
# Application environment (development, staging, production)
APP_ENV=development

# Enable debug mode (true for development, false for production)
DEBUG=true

# Allowed CORS origins (comma-separated for multiple origins)
# Example: http://localhost:3000,https://yourdomain.com
ALLOWED_ORIGINS=http://localhost:3000

# =============================================================================
# File Upload Configuration
# =============================================================================
# Maximum file size in bytes (default: 10MB = 10485760 bytes)
# Adjust based on your needs and server capacity
MAX_FILE_SIZE=10485760

# Allowed file types (comma-separated MIME types)
# Currently supports: JPEG, PNG, WebP
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# =============================================================================
# Optional: Additional Configuration
# =============================================================================
# Uncomment and configure these if needed

# Custom port for backend (default: 8000)
# BACKEND_PORT=8000

# Custom port for frontend (default: 3000)
# FRONTEND_PORT=3000

# Email configuration (for notifications - future feature)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password

# Sentry DSN for error tracking (production)
# SENTRY_DSN=https://your-sentry-dsn-here