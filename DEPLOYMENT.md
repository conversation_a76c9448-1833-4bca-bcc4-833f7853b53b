# ShareX Production Deployment Guide

## Prerequisites

### System Requirements
- Docker 20.10+ and Docker Compose 2.0+
- Minimum 2GB RAM, 2 CPU cores
- 20GB+ disk space
- Ubuntu 20.04+ or similar Linux distribution

### External Services
- AWS S3 bucket for photo storage
- Domain name with DNS access
- SSL certificate (Let's Encrypt recommended)

## Pre-Deployment Setup

### 1. Environment Configuration
```bash
# Copy and configure environment variables
cp .env.example .env

# Edit .env with production values
nano .env
```

**Critical Environment Variables:**
- `POSTGRES_PASSWORD`: Strong database password
- `JWT_SECRET_KEY`: Generate with `openssl rand -hex 32`
- `AWS_ACCESS_KEY_ID` & `AWS_SECRET_ACCESS_KEY`: AWS credentials
- `AWS_S3_BUCKET_NAME`: Your S3 bucket name
- `ALLOWED_ORIGINS`: Your domain (e.g., https://yourdomain.com)
- `APP_ENV=production`
- `DEBUG=false`

### 2. SSL Certificate Setup
```bash
# Create SSL directory
mkdir -p ssl

# Option 1: Let's Encrypt (recommended)
certbot certonly --standalone -d yourdomain.com
cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem ssl/cert.pem
cp /etc/letsencrypt/live/yourdomain.com/privkey.pem ssl/key.pem

# Option 2: Self-signed (development only)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ssl/key.pem -out ssl/cert.pem
```

### 3. AWS S3 Setup
```bash
# Create S3 bucket
aws s3 mb s3://your-sharex-bucket-name

# Set bucket policy for public read access to photos
aws s3api put-bucket-policy --bucket your-sharex-bucket-name --policy '{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::your-sharex-bucket-name/photos/*"
    }
  ]
}'
```

## Production Deployment

### 1. Build and Deploy
```bash
# Pull latest code
git pull origin main

# Build and start production containers
docker-compose -f docker-compose.prod.yml up -d --build

# Check container status
docker-compose -f docker-compose.prod.yml ps
```

### 2. Database Migration
```bash
# Run database migrations
docker-compose -f docker-compose.prod.yml exec backend alembic upgrade head

# Create admin user (optional)
docker-compose -f docker-compose.prod.yml exec backend python -c "
from app.database import get_db
from app.models.user import User
from app.core.security import get_password_hash
from sqlalchemy.orm import Session

db = next(get_db())
admin = User(
    email='<EMAIL>',
    hashed_password=get_password_hash('your-secure-password'),
    full_name='Admin User',
    role='admin',
    is_active=True
)
db.add(admin)
db.commit()
print('Admin user created')
"
```

### 3. Health Checks
```bash
# Check all services are healthy
docker-compose -f docker-compose.prod.yml exec nginx curl -f http://localhost/health
docker-compose -f docker-compose.prod.yml exec backend curl -f http://localhost:8000/health

# Check logs
docker-compose -f docker-compose.prod.yml logs -f
```

## Security Checklist

### ✅ Environment Security
- [ ] Strong passwords for all services
- [ ] JWT secret key is cryptographically secure
- [ ] AWS credentials have minimal required permissions
- [ ] Debug mode disabled in production
- [ ] CORS origins restricted to your domain

### ✅ Network Security
- [ ] Database and Redis not exposed to public internet
- [ ] Internal Docker networks configured
- [ ] Rate limiting enabled on API endpoints
- [ ] SSL/TLS encryption enabled

### ✅ Application Security
- [ ] File upload restrictions in place
- [ ] Input validation on all endpoints
- [ ] SQL injection protection (SQLAlchemy ORM)
- [ ] XSS protection headers configured
- [ ] CSRF protection enabled

### ✅ Infrastructure Security
- [ ] Non-root users in all containers
- [ ] Security headers configured in nginx
- [ ] Regular security updates applied
- [ ] Backup strategy implemented

## Monitoring and Maintenance

### Log Management
```bash
# View application logs
docker-compose -f docker-compose.prod.yml logs -f backend
docker-compose -f docker-compose.prod.yml logs -f celery_worker

# Rotate logs (add to crontab)
docker system prune -f
```

### Backup Strategy
```bash
# Database backup
docker-compose -f docker-compose.prod.yml exec postgres pg_dump -U $POSTGRES_USER $POSTGRES_DB > backup_$(date +%Y%m%d).sql

# S3 backup (photos are already in S3)
aws s3 sync s3://your-sharex-bucket-name s3://your-backup-bucket-name
```

### Updates
```bash
# Update application
git pull origin main
docker-compose -f docker-compose.prod.yml up -d --build

# Update system packages
apt update && apt upgrade -y
```

## Troubleshooting

### Common Issues
1. **Container won't start**: Check logs with `docker-compose logs [service]`
2. **Database connection failed**: Verify DATABASE_URL and postgres health
3. **File upload fails**: Check AWS credentials and S3 bucket permissions
4. **SSL certificate issues**: Verify certificate files and nginx configuration

### Performance Tuning
- Adjust worker processes in backend Dockerfile.prod
- Configure nginx worker_connections based on traffic
- Monitor resource usage with `docker stats`
- Scale services with `docker-compose up -d --scale backend=3`

## Support
For issues and questions, check the project documentation or create an issue in the repository.
