# ShareX - Modern Photo Sharing Platform

ShareX is a modern, secure photo sharing platform that allows moderators to create albums, upload photos, and share them with users through secure, time-limited links with access codes.

## 🚀 Features

### For Admins
- **Moderator Management**: Create, edit, and manage moderator accounts
- **Content Overview**: View all moderator content (read-only)
- **Statistics Dashboard**: Monitor platform usage and activity
- **User Management**: Complete control over moderator permissions

### For Moderators
- **Photo Upload**: Drag-and-drop photo uploads with automatic thumbnail generation
- **Album Management**: Create and organize photos into albums
- **Secure Sharing**: Generate time-limited links with 6-digit access codes
- **Link Management**: View, manage, and revoke active sharing links
- **Real-time Chat**: Communicate with users accessing shared content

### For Users
- **Secure Access**: Access shared content using links and access codes
- **Modern Gallery**: Beautiful, responsive photo viewing experience
- **Interactive Chat**: Ask questions or discuss photos with moderators
- **Download Support**: Download individual photos or entire albums

## 🏗️ Architecture

- **Frontend**: React 18 + TypeScript + Vite + Shadcn UI + Tailwind CSS
- **Backend**: FastAPI + Python 3.11 + SQLAlchemy + Alembic
- **Database**: PostgreSQL 15
- **Cache**: Redis 7
- **Storage**: AWS S3 for photo storage
- **Background Tasks**: Celery with Redis broker
- **Reverse Proxy**: Nginx
- **Containerization**: Docker + Docker Compose

## 🛠️ Quick Start

### Prerequisites
- Docker and Docker Compose
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd sharex
   ```

2. **Environment Setup**
   ```bash
   # Copy environment file
   cp .env.example .env
   
   # Update .env with your AWS S3 credentials and other settings
   nano .env
   ```

3. **Start the Application**
   ```bash
   # Build and start all services
   docker-compose up --build
   
   # Or run in background
   docker-compose up --build -d
   ```

4. **Access the Application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

### First Time Setup

1. **Database Migration**
   ```bash
   # Run database migrations
   docker-compose exec backend alembic upgrade head
   
   # Create first admin account
   docker-compose exec backend python -c "
   from app.utils.seed import create_admin_user
   create_admin_user()
   "
   ```

2. **Default Admin Credentials**
   - Email: `<EMAIL>`
   - Password: `admin123`
   
   **⚠️ Change these credentials immediately after first login!**

## 📁 Project Structure

```
sharex/
├── .env                    # Environment variables
├── .env.example           # Environment template
├── docker-compose.yml     # Docker services configuration
├── .gitignore            # Git ignore rules
├── README.md             # This file
├── DEVELOPMENT.md        # Development guide
├── nginx/                # Nginx reverse proxy
│   ├── Dockerfile
│   └── nginx.conf
├── backend/              # FastAPI backend
│   ├── Dockerfile
│   ├── requirements.txt
│   ├── alembic/         # Database migrations
│   └── app/             # Application code
│       ├── main.py      # FastAPI app entry point
│       ├── config.py    # Configuration
│       ├── models/      # Database models
│       ├── schemas/     # Pydantic schemas
│       ├── routers/     # API routes
│       ├── services/    # Business logic
│       ├── utils/       # Utilities
│       └── websocket/   # Socket.IO handlers
└── frontend/            # React frontend
    ├── Dockerfile
    ├── package.json
    ├── tsconfig.json
    ├── vite.config.ts
    ├── tailwind.config.js
    └── src/             # Source code
        ├── components/  # React components
        ├── pages/       # Page components
        ├── hooks/       # Custom hooks
        ├── lib/         # Utilities
        ├── store/       # State management
        └── types/       # TypeScript types
```

## 🔧 Development

See [DEVELOPMENT.md](DEVELOPMENT.md) for detailed development instructions.

## 🌐 API Endpoints

### Authentication
- `POST /api/auth/admin/login` - Admin login
- `POST /api/auth/moderator/signup` - Moderator registration
- `POST /api/auth/moderator/login` - Moderator login
- `GET /api/auth/me` - Get current user
- `POST /api/auth/refresh` - Refresh JWT token

### Admin Routes
- `GET /api/admin/moderators` - List all moderators
- `POST /api/admin/moderators` - Create moderator
- `PUT /api/admin/moderators/:id` - Update moderator
- `DELETE /api/admin/moderators/:id` - Delete moderator
- `GET /api/admin/statistics` - Platform statistics

### Moderator Routes
- `GET /api/moderator/albums` - List albums
- `POST /api/moderator/albums` - Create album
- `PUT /api/moderator/albums/:id` - Update album
- `DELETE /api/moderator/albums/:id` - Delete album
- `POST /api/moderator/photos/upload` - Upload photos
- `DELETE /api/moderator/photos/:id` - Delete photo
- `POST /api/moderator/shared-links` - Create sharing link
- `GET /api/moderator/shared-links` - List sharing links
- `DELETE /api/moderator/shared-links/:id` - Revoke link

### Public Access
- `POST /api/access/validate` - Validate access code
- `GET /api/access/content/:token` - Get shared content

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Rate Limiting**: API rate limiting to prevent abuse
- **Input Validation**: Comprehensive input validation and sanitization
- **CORS Protection**: Properly configured CORS policies
- **Security Headers**: Security headers via Nginx
- **Access Codes**: 6-digit codes for secure content access
- **Link Expiry**: Automatic link expiration (max 2 days)
- **File Validation**: Image type and size validation

## 📊 Monitoring & Logging

- **Health Checks**: Built-in health checks for all services
- **Structured Logging**: JSON-formatted logs for easy parsing
- **Error Tracking**: Comprehensive error handling and logging
- **Performance Monitoring**: Request timing and performance metrics

## 🚀 Deployment

### Production Considerations

1. **Environment Variables**: Update all production values in `.env`
2. **SSL/TLS**: Configure SSL certificates in Nginx
3. **Database**: Use managed PostgreSQL service
4. **Storage**: Configure AWS S3 with proper IAM policies
5. **Monitoring**: Set up log aggregation and monitoring
6. **Backups**: Implement database backup strategy

### Docker Production Build

```bash
# Production build
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up --build -d
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, please open an issue on GitHub or contact the development team.

## 🔄 Version History

- **v1.0.0** - Initial release with core functionality
  - Admin dashboard
  - Moderator management
  - Photo upload and album management
  - Secure link sharing
  - Real-time chat
  - Background task processing

---

**Built with ❤️ using modern web technologies**