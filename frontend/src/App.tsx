import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Navigate, Route, BrowserRouter as Router, Routes } from 'react-router-dom'
import { Toaster } from 'sonner'
import ProtectedRoute from './components/ProtectedRoute'
import { AuthProvider } from './contexts/AuthContext'
import HomePage from './pages/HomePage'
import AdminDashboard from './pages/admin/AdminDashboard'
import AdminLogin from './pages/auth/AdminLogin'
import ModeratorLogin from './pages/auth/ModeratorLogin'
import ModeratorSignup from './pages/auth/ModeratorSignup'
import ModeratorDashboard from './pages/moderator/ModeratorDashboard'
import AccessPage from './pages/public/AccessPage'
import GalleryView from './pages/public/GalleryView'

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
    },
  },
})

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Router>
          <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
            <Routes>
              {/* Public Routes */}
              <Route path="/" element={<HomePage />} />
              <Route path="/auth/admin/login" element={<AdminLogin />} />
              <Route path="/auth/moderator/login" element={<ModeratorLogin />} />
              <Route path="/auth/moderator/signup" element={<ModeratorSignup />} />
              <Route path="/access" element={<AccessPage />} />
              <Route path="/gallery/:token" element={<GalleryView />} />
              
              {/* Protected Admin Routes */}
              <Route 
                path="/admin/*" 
                element={
                  <ProtectedRoute requiredRole="admin">
                    <AdminDashboard />
                  </ProtectedRoute>
                } 
              />
              
              {/* Protected Moderator Routes */}
              <Route 
                path="/moderator/*" 
                element={
                  <ProtectedRoute requiredRole="moderator">
                    <ModeratorDashboard />
                  </ProtectedRoute>
                } 
              />
              
              {/* Catch all route */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </div>
        </Router>
        <Toaster position="top-right" />
      </AuthProvider>
    </QueryClientProvider>
  )
}

export default App