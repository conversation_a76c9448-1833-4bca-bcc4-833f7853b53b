import { useEffect, useRef, useState } from 'react'
import { io, Socket } from 'socket.io-client'

interface Message {
  id: number
  content: string
  sender_name: string
  is_from_user: boolean
  created_at: string
  sender_id?: number
}

interface UseSocketProps {
  sharedLinkToken?: string
  userId?: number
  userName?: string
}

export function useSocket({ sharedLinkToken, userId, userName }: UseSocketProps) {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const [typingUsers, setTypingUsers] = useState<string[]>([])
  const typingTimeoutRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    if (!sharedLinkToken) return

    const newSocket = io('http://localhost:8000', {
      auth: {
        shared_link_token: sharedLinkToken,
        user_id: userId,
        user_name: userName || 'Anonymous'
      }
    })

    newSocket.on('connect', () => {
      setIsConnected(true)
      console.log('Connected to chat server')
    })

    newSocket.on('disconnect', () => {
      setIsConnected(false)
      console.log('Disconnected from chat server')
    })

    newSocket.on('new_message', (message: Message) => {
      setMessages(prev => [...prev, message])
    })

    newSocket.on('message_history', (data: { messages: Message[] }) => {
      setMessages(data.messages)
    })

    newSocket.on('user_typing', (data: { user_name: string, is_moderator: boolean }) => {
      setTypingUsers(prev => {
        if (!prev.includes(data.user_name)) {
          return [...prev, data.user_name]
        }
        return prev
      })
    })

    newSocket.on('user_stopped_typing', (data: { user_name: string }) => {
      setTypingUsers(prev => prev.filter(name => name !== data.user_name))
    })

    newSocket.on('error', (error: { message: string }) => {
      console.error('Socket error:', error.message)
    })

    setSocket(newSocket)

    // Get message history
    newSocket.emit('get_messages', {})

    return () => {
      newSocket.close()
    }
  }, [sharedLinkToken, userId, userName])

  const sendMessage = (content: string) => {
    if (socket && content.trim()) {
      socket.emit('send_message', { message: content.trim() })
    }
  }

  const startTyping = () => {
    if (socket && !isTyping) {
      setIsTyping(true)
      socket.emit('typing_start', {})
      
      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }
      
      // Set timeout to stop typing
      typingTimeoutRef.current = setTimeout(() => {
        stopTyping()
      }, 3000)
    }
  }

  const stopTyping = () => {
    if (socket && isTyping) {
      setIsTyping(false)
      socket.emit('typing_stop', {})
      
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }
    }
  }

  return {
    socket,
    messages,
    isConnected,
    isTyping,
    typingUsers,
    sendMessage,
    startTyping,
    stopTyping
  }
}