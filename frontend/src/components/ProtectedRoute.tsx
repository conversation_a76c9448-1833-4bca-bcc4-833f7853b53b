import { useAuth } from '@/contexts/AuthContext'
import { Navigate, useLocation } from 'react-router-dom'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: 'admin' | 'moderator'
}

export default function ProtectedRoute({ children, requiredRole }: ProtectedRouteProps) {
  const { isAuthenticated, isAdmin, isModerator, isLoading } = useAuth()
  const location = useLocation()

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    // Redirect to appropriate login page based on required role
    const loginPath = requiredRole === 'admin' ? '/auth/admin/login' : '/auth/moderator/login'
    return <Navigate to={loginPath} state={{ from: location }} replace />
  }

  // Check role permissions
  if (requiredRole === 'admin' && !isAdmin) {
    return <Navigate to="/auth/admin/login" replace />
  }

  if (requiredRole === 'moderator' && !isModerator) {
    return <Navigate to="/auth/moderator/login" replace />
  }

  return <>{children}</>
}