import { Button } from '@/components/ui/button'
import { useState } from 'react'
import { Link } from 'react-router-dom'

export default function HomePage() {
  const [count, setCount] = useState(0)

  return (
    <>
      {/* Modern Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">S</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                  ShareX
                </h1>
                <p className="text-sm text-gray-500">Modern Photo Sharing Platform</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link to="/auth/admin/login">
                <Button variant="ghost" className="font-medium">
                  Admin Login
                </Button>
              </Link>
              <Link to="/auth/moderator/login">
                <Button variant="outline" className="font-medium">
                  Moderator Login
                </Button>
              </Link>
              <Link to="/auth/moderator/signup">
                <Button variant="gradient" className="font-semibold">
                  Get Started
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h2 className="text-5xl font-bold text-gray-900 mb-6 leading-tight">
            Share Photos with
            <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent block">
              Secure Access Codes
            </span>
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
            Create beautiful photo albums, generate secure sharing links with time-limited access codes, 
            and chat with viewers in real-time. Perfect for photographers, event organizers, and content creators.
          </p>
          <div className="flex items-center justify-center space-x-4">
            <Button 
              variant="gradient"
              size="xl"
              onClick={() => setCount((count) => count + 1)}
              className="font-semibold"
            >
              Demo Counter: {count}
            </Button>
            <Button variant="outline" size="xl" className="font-semibold">
              View Documentation
            </Button>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-gray-200/50 hover:shadow-lg transition-all duration-300">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-4">
              <span className="text-white font-bold">📸</span>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">Smart Albums</h3>
            <p className="text-gray-600">Organize photos into beautiful albums with drag-and-drop functionality and automatic thumbnail generation.</p>
          </div>
          
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-gray-200/50 hover:shadow-lg transition-all duration-300">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mb-4">
              <span className="text-white font-bold">🔐</span>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">Secure Sharing</h3>
            <p className="text-gray-600">Generate time-limited links with 6-digit access codes. Maximum 2-day expiry for enhanced security.</p>
          </div>
          
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-gray-200/50 hover:shadow-lg transition-all duration-300">
            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-600 rounded-xl flex items-center justify-center mb-4">
              <span className="text-white font-bold">💬</span>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">Real-time Chat</h3>
            <p className="text-gray-600">Communicate with viewers directly through integrated chat. Perfect for feedback and discussions.</p>
          </div>
        </div>

        {/* Status Section */}
        <div className="bg-white/80 backdrop-blur-md rounded-2xl p-8 border border-gray-200/50 text-center">
          <div className="inline-flex items-center space-x-2 bg-green-100 text-green-800 px-4 py-2 rounded-full font-medium">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>System Status: All Services Operational</span>
          </div>
          <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <div className="font-semibold text-gray-900">Backend API</div>
              <div className="text-green-600">✓ Healthy</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-gray-900">Database</div>
              <div className="text-green-600">✓ Connected</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-gray-900">Authentication</div>
              <div className="text-green-600">✓ Active</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-gray-900">File Storage</div>
              <div className="text-yellow-600">⚠ Pending</div>
            </div>
          </div>
        </div>

        {/* Quick Access Section */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Quick Access</h3>
          <div className="flex items-center justify-center space-x-6">
            <Link to="/access">
              <Button variant="outline" size="lg" className="font-medium">
                🔗 Access Shared Content
              </Button>
            </Link>
            <Link to="/auth/moderator/signup">
              <Button variant="gradient" size="lg" className="font-medium">
                👤 Create Moderator Account
              </Button>
            </Link>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white/50 backdrop-blur-sm border-t border-gray-200/50 mt-16">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="text-center text-gray-600">
            <p>Built with ❤️ using React, FastAPI, PostgreSQL, and modern web technologies</p>
          </div>
        </div>
      </footer>
    </>
  )
}