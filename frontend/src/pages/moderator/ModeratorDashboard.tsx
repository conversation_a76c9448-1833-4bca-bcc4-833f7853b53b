import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState } from 'react'

interface Album {
  id: number
  name: string
  description?: string
  photo_count: number
  cover_photo_id?: number
  created_at: string
  updated_at?: string
}

interface Photo {
  id: number
  filename: string
  original_filename: string
  file_size: number
  width?: number
  height?: number
  thumbnail_url?: string
  photo_url: string
  created_at: string
}

interface SharedLink {
  id: number
  share_url: string
  access_code: string
  content_type: string
  content_name: string
  is_active: boolean
  is_expired: boolean
  access_count: number
  expires_at: string
  created_at: string
}

export default function ModeratorDashboard() {
  const { user, logout } = useAuth()
  const [albums, setAlbums] = useState<Album[]>([])
  const [photos, setPhotos] = useState<Photo[]>([])
  const [sharedLinks, setSharedLinks] = useState<SharedLink[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'albums' | 'photos' | 'links' | 'upload'>('albums')
  const [dragActive, setDragActive] = useState(false)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const token = localStorage.getItem('access_token')
      
      // Fetch albums
      const albumsResponse = await fetch('http://localhost:8000/api/moderator/albums', {
        headers: { 'Authorization': `Bearer ${token}` }
      })
      if (albumsResponse.ok) {
        const albumsData = await albumsResponse.json()
        setAlbums(albumsData)
      }

      // Fetch photos
      const photosResponse = await fetch('http://localhost:8000/api/moderator/photos', {
        headers: { 'Authorization': `Bearer ${token}` }
      })
      if (photosResponse.ok) {
        const photosData = await photosResponse.json()
        setPhotos(photosData)
      }

      // Fetch shared links
      const linksResponse = await fetch('http://localhost:8000/api/moderator/shared-links', {
        headers: { 'Authorization': `Bearer ${token}` }
      })
      if (linksResponse.ok) {
        const linksData = await linksResponse.json()
        setSharedLinks(linksData)
      }
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files)
    }
  }

  const handleFiles = (files: FileList) => {
    console.log('Files to upload:', files)
    // TODO: Implement file upload
  }

  const handleLogout = () => {
    logout()
    window.location.href = '/'
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">S</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                  ShareX Studio
                </h1>
                <p className="text-sm text-gray-500">Moderator Dashboard</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Welcome, {user?.full_name || user?.email}
              </span>
              <Button variant="outline" onClick={handleLogout}>
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Navigation Tabs */}
        <div className="flex space-x-1 mb-8 bg-white/60 backdrop-blur-sm rounded-xl p-1 border border-gray-200/50">
          <button
            onClick={() => setActiveTab('albums')}
            className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              activeTab === 'albums'
                ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            📁 Albums ({albums.length})
          </button>
          <button
            onClick={() => setActiveTab('photos')}
            className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              activeTab === 'photos'
                ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            📸 Photos ({photos.length})
          </button>
          <button
            onClick={() => setActiveTab('upload')}
            className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              activeTab === 'upload'
                ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            ⬆️ Upload
          </button>
          <button
            onClick={() => setActiveTab('links')}
            className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              activeTab === 'links'
                ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            🔗 Shared Links ({sharedLinks.length})
          </button>
        </div>

        {/* Albums Tab */}
        {activeTab === 'albums' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">My Albums</h2>
              <Button variant="gradient">
                + Create Album
              </Button>
            </div>

            {albums.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {albums.map((album) => (
                  <Card key={album.id} className="bg-white/60 backdrop-blur-sm border-gray-200/50 hover:shadow-lg transition-all duration-300">
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span>{album.name}</span>
                        <span className="text-sm font-normal text-gray-500">
                          {album.photo_count} photos
                        </span>
                      </CardTitle>
                      {album.description && (
                        <CardDescription>{album.description}</CardDescription>
                      )}
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-400">
                          Created {new Date(album.created_at).toLocaleDateString()}
                        </span>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            Share
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
                <CardContent className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-gray-400 text-2xl">📁</span>
                  </div>
                  <p className="text-gray-500 mb-4">No albums created yet</p>
                  <Button variant="gradient">
                    Create Your First Album
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {/* Photos Tab */}
        {activeTab === 'photos' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">My Photos</h2>
              <Button variant="gradient" onClick={() => setActiveTab('upload')}>
                + Upload Photos
              </Button>
            </div>

            {photos.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {photos.map((photo) => (
                  <Card key={photo.id} className="bg-white/60 backdrop-blur-sm border-gray-200/50 hover:shadow-lg transition-all duration-300 overflow-hidden">
                    <div className="aspect-square bg-gray-100 relative">
                      {photo.thumbnail_url ? (
                        <img
                          src={photo.thumbnail_url}
                          alt={photo.original_filename}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <span className="text-gray-400 text-2xl">📸</span>
                        </div>
                      )}
                      <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-all duration-200 flex items-center justify-center opacity-0 hover:opacity-100">
                        <Button variant="outline" size="sm" className="bg-white/90">
                          View
                        </Button>
                      </div>
                    </div>
                    <CardContent className="p-3">
                      <p className="text-xs text-gray-600 truncate">{photo.original_filename}</p>
                      <p className="text-xs text-gray-400">
                        {(photo.file_size / 1024 / 1024).toFixed(1)} MB
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
                <CardContent className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-gray-400 text-2xl">📸</span>
                  </div>
                  <p className="text-gray-500 mb-4">No photos uploaded yet</p>
                  <Button variant="gradient" onClick={() => setActiveTab('upload')}>
                    Upload Your First Photos
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {/* Upload Tab */}
        {activeTab === 'upload' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Upload Photos</h2>
            
            <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
              <CardContent className="p-8">
                <div
                  className={`border-2 border-dashed rounded-xl p-12 text-center transition-all duration-200 ${
                    dragActive
                      ? 'border-indigo-500 bg-indigo-50'
                      : 'border-gray-300 hover:border-indigo-400 hover:bg-gray-50'
                  }`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  <div className="w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-white text-2xl">📤</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Drag & Drop Photos Here
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Or click to browse and select multiple files
                  </p>
                  <Button variant="gradient" size="lg">
                    Choose Files
                  </Button>
                  <p className="text-sm text-gray-500 mt-4">
                    Supports JPEG, PNG, WebP • Max 10MB per file
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Shared Links Tab */}
        {activeTab === 'links' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">Shared Links</h2>
              <Button variant="gradient">
                + Create Link
              </Button>
            </div>

            {sharedLinks.length > 0 ? (
              <div className="space-y-4">
                {sharedLinks.map((link) => (
                  <Card key={link.id} className="bg-white/60 backdrop-blur-sm border-gray-200/50">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <span className="text-lg">
                              {link.content_type === 'album' ? '📁' : '📸'}
                            </span>
                            <div>
                              <p className="font-medium text-gray-900">{link.content_name}</p>
                              <p className="text-sm text-gray-500">
                                {link.content_type} • {link.access_count} views
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-4 text-sm">
                            <span className="font-mono bg-gray-100 px-2 py-1 rounded">
                              Code: {link.access_code}
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              link.is_expired 
                                ? 'bg-red-100 text-red-800'
                                : link.is_active 
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-gray-100 text-gray-800'
                            }`}>
                              {link.is_expired ? 'Expired' : link.is_active ? 'Active' : 'Inactive'}
                            </span>
                            <span className="text-gray-500">
                              Expires {new Date(link.expires_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button variant="outline" size="sm">
                            Copy Link
                          </Button>
                          <Button variant="outline" size="sm">
                            Copy Code
                          </Button>
                          <Button variant="outline" size="sm">
                            Revoke
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
                <CardContent className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-gray-400 text-2xl">🔗</span>
                  </div>
                  <p className="text-gray-500 mb-4">No shared links created yet</p>
                  <Button variant="gradient">
                    Create Your First Link
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>
    </div>
  )
}