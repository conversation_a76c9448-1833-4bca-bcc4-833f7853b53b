import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState } from 'react'

interface Album {
  id: number
  name: string
  description?: string
  photo_count: number
  cover_photo_id?: number
  created_at: string
  updated_at?: string
}

interface Photo {
  id: number
  filename: string
  original_filename: string
  file_size: number
  width?: number
  height?: number
  thumbnail_url?: string
  photo_url: string
  created_at: string
}

interface SharedLink {
  id: number
  share_url: string
  access_code: string
  content_type: string
  content_name: string
  is_active: boolean
  is_expired: boolean
  access_count: number
  expires_at: string
  created_at: string
}

export default function ModeratorDashboard() {
  const { user, logout } = useAuth()
  const [albums, setAlbums] = useState<Album[]>([])
  const [photos, setPhotos] = useState<Photo[]>([])
  const [sharedLinks, setSharedLinks] = useState<SharedLink[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'albums' | 'photos' | 'links' | 'upload' | 'chat'>('albums')
  const [dragActive, setDragActive] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<{[key: string]: number}>({})
  const [isUploading, setIsUploading] = useState(false)

  // Album management state
  const [showCreateAlbum, setShowCreateAlbum] = useState(false)
  const [newAlbumName, setNewAlbumName] = useState('')
  const [newAlbumDescription, setNewAlbumDescription] = useState('')
  const [isCreatingAlbum, setIsCreatingAlbum] = useState(false)

  // Link generation state
  const [showCreateLink, setShowCreateLink] = useState(false)
  const [selectedContent, setSelectedContent] = useState<{type: 'album' | 'photo', id: number, name: string} | null>(null)
  const [linkExpiry, setLinkExpiry] = useState('24') // hours
  const [isCreatingLink, setIsCreatingLink] = useState(false)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const token = localStorage.getItem('access_token')

      // Fetch albums
      const albumsResponse = await fetch('http://localhost:8000/api/moderator/albums', {
        headers: { 'Authorization': `Bearer ${token}` }
      })
      if (albumsResponse.ok) {
        const albumsData = await albumsResponse.json()
        setAlbums(albumsData)
      }

      // Fetch photos
      const photosResponse = await fetch('http://localhost:8000/api/moderator/photos', {
        headers: { 'Authorization': `Bearer ${token}` }
      })
      if (photosResponse.ok) {
        const photosData = await photosResponse.json()
        setPhotos(photosData)
      }

      // Fetch shared links
      const linksResponse = await fetch('http://localhost:8000/api/moderator/shared-links', {
        headers: { 'Authorization': `Bearer ${token}` }
      })
      if (linksResponse.ok) {
        const linksData = await linksResponse.json()
        setSharedLinks(linksData)
      }
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files)
    }
  }

  const handleFiles = async (files: FileList) => {
    if (files.length === 0) return

    setIsUploading(true)
    const token = localStorage.getItem('access_token')

    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      const fileId = `${file.name}-${Date.now()}`

      // Validate file type
      if (!['image/jpeg', 'image/png', 'image/webp'].includes(file.type)) {
        console.error(`Invalid file type: ${file.type}`)
        continue
      }

      // Validate file size (10MB)
      if (file.size > 10 * 1024 * 1024) {
        console.error(`File too large: ${file.size} bytes`)
        continue
      }

      try {
        const formData = new FormData()
        formData.append('file', file)

        setUploadProgress(prev => ({ ...prev, [fileId]: 0 }))

        const response = await fetch('http://localhost:8000/api/moderator/photos/upload', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          },
          body: formData
        })

        if (response.ok) {
          setUploadProgress(prev => ({ ...prev, [fileId]: 100 }))
          console.log(`Successfully uploaded: ${file.name}`)
        } else {
          console.error(`Failed to upload: ${file.name}`)
        }
      } catch (error) {
        console.error(`Error uploading ${file.name}:`, error)
      }
    }

    // Refresh photos list
    await fetchData()
    setIsUploading(false)
    setUploadProgress({})
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files)
    }
  }

  const createAlbum = async () => {
    if (!newAlbumName.trim()) return

    setIsCreatingAlbum(true)
    try {
      const token = localStorage.getItem('access_token')
      const response = await fetch('http://localhost:8000/api/moderator/albums', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          name: newAlbumName.trim(),
          description: newAlbumDescription.trim() || undefined
        })
      })

      if (response.ok) {
        setNewAlbumName('')
        setNewAlbumDescription('')
        setShowCreateAlbum(false)
        await fetchData() // Refresh albums list
      } else {
        console.error('Failed to create album')
      }
    } catch (error) {
      console.error('Error creating album:', error)
    } finally {
      setIsCreatingAlbum(false)
    }
  }

  const createSharedLink = async () => {
    if (!selectedContent) return

    setIsCreatingLink(true)
    try {
      const token = localStorage.getItem('access_token')
      const response = await fetch('http://localhost:8000/api/moderator/shared-links', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          content_type: selectedContent.type,
          content_id: selectedContent.id,
          expires_in_hours: parseInt(linkExpiry)
        })
      })

      if (response.ok) {
        setSelectedContent(null)
        setLinkExpiry('24')
        setShowCreateLink(false)
        await fetchData() // Refresh links list
      } else {
        console.error('Failed to create shared link')
      }
    } catch (error) {
      console.error('Error creating shared link:', error)
    } finally {
      setIsCreatingLink(false)
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      // Simple notification - you could replace with a proper toast
      const notification = document.createElement('div')
      notification.textContent = 'Copied to clipboard!'
      notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50'
      document.body.appendChild(notification)
      setTimeout(() => {
        document.body.removeChild(notification)
      }, 2000)
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  const revokeLink = async (linkId: number) => {
    try {
      const token = localStorage.getItem('access_token')
      const response = await fetch(`http://localhost:8000/api/moderator/shared-links/${linkId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        await fetchData() // Refresh links list
      } else {
        console.error('Failed to revoke link')
      }
    } catch (error) {
      console.error('Error revoking link:', error)
    }
  }

  const handleLogout = () => {
    logout()
    window.location.href = '/'
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">S</span>
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                  ShareX Studio
                </h1>
                <p className="text-sm text-gray-500">Moderator Dashboard</p>
              </div>
            </div>
            <div className="flex items-center space-x-2 sm:space-x-4">
              <span className="hidden sm:inline text-sm text-gray-600">
                Welcome, {user?.full_name || user?.email}
              </span>
              <Button variant="outline" onClick={handleLogout}>
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-8">
        {/* Navigation Tabs */}
        <div className="flex flex-wrap gap-1 mb-8 bg-white/60 backdrop-blur-sm rounded-xl p-1 border border-gray-200/50">
          <button
            onClick={() => setActiveTab('albums')}
            className={`px-3 sm:px-6 py-3 rounded-lg font-medium transition-all duration-200 text-sm sm:text-base ${
              activeTab === 'albums'
                ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            <span className="hidden sm:inline">📁 Albums ({albums.length})</span>
            <span className="sm:hidden">📁 Albums</span>
          </button>
          <button
            onClick={() => setActiveTab('photos')}
            className={`px-3 sm:px-6 py-3 rounded-lg font-medium transition-all duration-200 text-sm sm:text-base ${
              activeTab === 'photos'
                ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            <span className="hidden sm:inline">📸 Photos ({photos.length})</span>
            <span className="sm:hidden">📸 Photos</span>
          </button>
          <button
            onClick={() => setActiveTab('upload')}
            className={`px-3 sm:px-6 py-3 rounded-lg font-medium transition-all duration-200 text-sm sm:text-base ${
              activeTab === 'upload'
                ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            ⬆️ Upload
          </button>
          <button
            onClick={() => setActiveTab('links')}
            className={`px-3 sm:px-6 py-3 rounded-lg font-medium transition-all duration-200 text-sm sm:text-base ${
              activeTab === 'links'
                ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            <span className="hidden sm:inline">🔗 Shared Links ({sharedLinks.length})</span>
            <span className="sm:hidden">🔗 Links</span>
          </button>
          <button
            onClick={() => setActiveTab('chat')}
            className={`px-3 sm:px-6 py-3 rounded-lg font-medium transition-all duration-200 text-sm sm:text-base ${
              activeTab === 'chat'
                ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            💬 Chat
          </button>
        </div>

        {/* Albums Tab */}
        {activeTab === 'albums' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">My Albums</h2>
              <div>
                <button
                  onClick={() => setShowCreateAlbum(true)}
                  className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg hover:from-indigo-700 hover:to-purple-700 hover:shadow-xl h-10 rounded-md px-8"
                >
                  + Create Album
                </button>

                {/* Create Album Modal */}
                {showCreateAlbum && (
                  <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
                    <div className="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-white p-6 shadow-lg rounded-lg">
                      <div className="flex flex-col space-y-1.5 text-center sm:text-left">
                        <h2 className="text-lg font-semibold leading-none tracking-tight">Create New Album</h2>
                        <p className="text-sm text-gray-600">Add a new album to organize your photos</p>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Album Name
                          </label>
                          <input
                            type="text"
                            value={newAlbumName}
                            onChange={(e) => setNewAlbumName(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                            placeholder="Enter album name"
                            autoFocus
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Description (Optional)
                          </label>
                          <textarea
                            value={newAlbumDescription}
                            onChange={(e) => setNewAlbumDescription(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                            placeholder="Enter album description"
                            rows={3}
                          />
                        </div>
                      </div>

                      <div className="flex justify-end space-x-3">
                        <button
                          onClick={() => setShowCreateAlbum(false)}
                          disabled={isCreatingAlbum}
                          className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={createAlbum}
                          disabled={isCreatingAlbum || !newAlbumName.trim()}
                          className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg hover:from-indigo-700 hover:to-purple-700 hover:shadow-xl h-9 px-4 py-2"
                        >
                          {isCreatingAlbum ? 'Creating...' : 'Create Album'}
                        </button>
                      </div>

                      <button
                        onClick={() => setShowCreateAlbum(false)}
                        className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                      >
                        <span className="h-4 w-4">✕</span>
                        <span className="sr-only">Close</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {albums.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {albums.map((album) => (
                  <Card key={album.id} className="bg-white/60 backdrop-blur-sm border-gray-200/50 hover:shadow-lg transition-all duration-300">
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span>{album.name}</span>
                        <span className="text-sm font-normal text-gray-500">
                          {album.photo_count} photos
                        </span>
                      </CardTitle>
                      {album.description && (
                        <CardDescription>{album.description}</CardDescription>
                      )}
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-400">
                          Created {new Date(album.created_at).toLocaleDateString()}
                        </span>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            Share
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
                <CardContent className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-gray-400 text-2xl">📁</span>
                  </div>
                  <p className="text-gray-500 mb-4">No albums created yet</p>
                  <button
                    onClick={() => setShowCreateAlbum(true)}
                    className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg hover:from-indigo-700 hover:to-purple-700 hover:shadow-xl h-10 rounded-md px-8"
                  >
                    Create Your First Album
                  </button>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {/* Photos Tab */}
        {activeTab === 'photos' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">My Photos</h2>
              <Button variant="gradient" onClick={() => setActiveTab('upload')}>
                + Upload Photos
              </Button>
            </div>

            {photos.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {photos.map((photo) => (
                  <Card key={photo.id} className="bg-white/60 backdrop-blur-sm border-gray-200/50 hover:shadow-lg transition-all duration-300 overflow-hidden">
                    <div className="aspect-square bg-gray-100 relative">
                      {photo.thumbnail_url ? (
                        <img
                          src={photo.thumbnail_url}
                          alt={photo.original_filename}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <span className="text-gray-400 text-2xl">📸</span>
                        </div>
                      )}
                      <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-all duration-200 flex items-center justify-center opacity-0 hover:opacity-100">
                        <Button variant="outline" size="sm" className="bg-white/90">
                          View
                        </Button>
                      </div>
                    </div>
                    <CardContent className="p-3">
                      <p className="text-xs text-gray-600 truncate">{photo.original_filename}</p>
                      <p className="text-xs text-gray-400">
                        {(photo.file_size / 1024 / 1024).toFixed(1)} MB
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
                <CardContent className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-gray-400 text-2xl">📸</span>
                  </div>
                  <p className="text-gray-500 mb-4">No photos uploaded yet</p>
                  <Button variant="gradient" onClick={() => setActiveTab('upload')}>
                    Upload Your First Photos
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {/* Upload Tab */}
        {activeTab === 'upload' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Upload Photos</h2>

            <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
              <CardContent className="p-8">
                <div
                  className={`border-2 border-dashed rounded-xl p-12 text-center transition-all duration-200 ${
                    dragActive
                      ? 'border-indigo-500 bg-indigo-50'
                      : 'border-gray-300 hover:border-indigo-400 hover:bg-gray-50'
                  }`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  <div className="w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-white text-2xl">
                      {isUploading ? '⏳' : '📤'}
                    </span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {isUploading ? 'Uploading Photos...' : 'Drag & Drop Photos Here'}
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Or click to browse and select multiple files
                  </p>
                  <input
                    type="file"
                    multiple
                    accept="image/jpeg,image/png,image/webp"
                    onChange={handleFileSelect}
                    className="hidden"
                    id="file-upload"
                    disabled={isUploading}
                  />
                  <label htmlFor="file-upload">
                    <Button
                      variant="gradient"
                      size="lg"
                      disabled={isUploading}
                      asChild
                    >
                      <span className="cursor-pointer">
                        {isUploading ? 'Uploading...' : 'Choose Files'}
                      </span>
                    </Button>
                  </label>
                  <p className="text-sm text-gray-500 mt-4">
                    Supports JPEG, PNG, WebP • Max 10MB per file
                  </p>

                  {/* Upload Progress */}
                  {Object.keys(uploadProgress).length > 0 && (
                    <div className="mt-6 space-y-2">
                      {Object.entries(uploadProgress).map(([fileId, progress]) => (
                        <div key={fileId} className="bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-gradient-to-r from-indigo-600 to-purple-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${progress}%` }}
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Shared Links Tab */}
        {activeTab === 'links' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">Shared Links</h2>
              <div>
                <button
                  onClick={() => setShowCreateLink(true)}
                  className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg hover:from-indigo-700 hover:to-purple-700 hover:shadow-xl h-10 rounded-md px-8"
                >
                  + Create Link
                </button>

                {/* Create Link Modal */}
                {showCreateLink && (
                  <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
                    <div className="fixed left-[50%] top-[50%] z-50 grid w-full max-w-2xl translate-x-[-50%] translate-y-[-50%] gap-4 border bg-white p-6 shadow-lg rounded-lg">
                      <div className="flex flex-col space-y-1.5 text-center sm:text-left">
                        <h2 className="text-lg font-semibold leading-none tracking-tight">Create Shared Link</h2>
                        <p className="text-sm text-gray-600">Select content to share and set expiry time</p>
                      </div>

                      <div className="space-y-6">
                        {/* Content Selection */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-3">
                            Select Content to Share
                          </label>

                          {/* Albums */}
                          {albums.length > 0 && (
                            <div className="mb-4">
                              <h4 className="text-sm font-medium text-gray-600 mb-2">Albums</h4>
                              <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto">
                                {albums.map((album) => (
                                  <div
                                    key={`album-${album.id}`}
                                    onClick={() => setSelectedContent({type: 'album', id: album.id, name: album.name})}
                                    className={`p-3 border rounded-lg cursor-pointer transition-all ${
                                      selectedContent?.type === 'album' && selectedContent?.id === album.id
                                        ? 'border-indigo-500 bg-indigo-50'
                                        : 'border-gray-200 hover:border-gray-300'
                                    }`}
                                  >
                                    <div className="flex items-center space-x-3">
                                      <span className="text-lg">📁</span>
                                      <div>
                                        <p className="font-medium text-gray-900">{album.name}</p>
                                        <p className="text-sm text-gray-500">{album.photo_count} photos</p>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Photos */}
                          {photos.length > 0 && (
                            <div>
                              <h4 className="text-sm font-medium text-gray-600 mb-2">Individual Photos</h4>
                              <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                                {photos.map((photo) => (
                                  <div
                                    key={`photo-${photo.id}`}
                                    onClick={() => setSelectedContent({type: 'photo', id: photo.id, name: photo.original_filename})}
                                    className={`p-2 border rounded-lg cursor-pointer transition-all ${
                                      selectedContent?.type === 'photo' && selectedContent?.id === photo.id
                                        ? 'border-indigo-500 bg-indigo-50'
                                        : 'border-gray-200 hover:border-gray-300'
                                    }`}
                                  >
                                    <div className="flex items-center space-x-2">
                                      <span className="text-sm">📸</span>
                                      <p className="text-sm font-medium text-gray-900 truncate">{photo.original_filename}</p>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {albums.length === 0 && photos.length === 0 && (
                            <p className="text-gray-500 text-center py-4">No content available to share. Create albums or upload photos first.</p>
                          )}
                        </div>

                        {/* Expiry Selection */}
                        {selectedContent && (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Link Expiry
                            </label>
                            <select
                              value={linkExpiry}
                              onChange={(e) => setLinkExpiry(e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                            >
                              <option value="1">1 hour</option>
                              <option value="6">6 hours</option>
                              <option value="12">12 hours</option>
                              <option value="24">24 hours (1 day)</option>
                              <option value="48">48 hours (2 days)</option>
                            </select>
                          </div>
                        )}
                      </div>

                      <div className="flex justify-end space-x-3">
                        <button
                          onClick={() => {
                            setShowCreateLink(false)
                            setSelectedContent(null)
                            setLinkExpiry('24')
                          }}
                          disabled={isCreatingLink}
                          className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={createSharedLink}
                          disabled={isCreatingLink || !selectedContent}
                          className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg hover:from-indigo-700 hover:to-purple-700 hover:shadow-xl h-9 px-4 py-2"
                        >
                          {isCreatingLink ? 'Creating...' : 'Create Link'}
                        </button>
                      </div>

                      <button
                        onClick={() => {
                          setShowCreateLink(false)
                          setSelectedContent(null)
                          setLinkExpiry('24')
                        }}
                        className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                      >
                        <span className="h-4 w-4">✕</span>
                        <span className="sr-only">Close</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {sharedLinks.length > 0 ? (
              <div className="space-y-4">
                {sharedLinks.map((link) => (
                  <Card key={link.id} className="bg-white/60 backdrop-blur-sm border-gray-200/50">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <span className="text-lg">
                              {link.content_type === 'album' ? '📁' : '📸'}
                            </span>
                            <div>
                              <p className="font-medium text-gray-900">{link.content_name}</p>
                              <p className="text-sm text-gray-500">
                                {link.content_type} • {link.access_count} views
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-4 text-sm">
                            <span className="font-mono bg-gray-100 px-2 py-1 rounded">
                              Code: {link.access_code}
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              link.is_expired
                                ? 'bg-red-100 text-red-800'
                                : link.is_active
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-gray-100 text-gray-800'
                            }`}>
                              {link.is_expired ? 'Expired' : link.is_active ? 'Active' : 'Inactive'}
                            </span>
                            <span className="text-gray-500">
                              Expires {new Date(link.expires_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => copyToClipboard(`http://localhost:3000/access?link=${link.share_url}`)}
                            className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs"
                          >
                            Copy Link
                          </button>
                          <button
                            onClick={() => copyToClipboard(link.access_code)}
                            className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs"
                          >
                            Copy Code
                          </button>
                          <button
                            onClick={() => revokeLink(link.id)}
                            className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs"
                          >
                            Revoke
                          </button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
                <CardContent className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-gray-400 text-2xl">🔗</span>
                  </div>
                  <p className="text-gray-500 mb-4">No shared links created yet</p>
                  <button
                    onClick={() => setShowCreateLink(true)}
                    className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg hover:from-indigo-700 hover:to-purple-700 hover:shadow-xl h-10 rounded-md px-8"
                  >
                    Create Your First Link
                  </button>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {/* Chat Tab */}
        {activeTab === 'chat' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Chat Management</h2>

            <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
              <CardHeader>
                <CardTitle>Active Conversations</CardTitle>
                <CardDescription>Manage conversations with users accessing your shared content</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-gray-400 text-2xl">💬</span>
                  </div>
                  <p className="text-gray-500 mb-4">No active conversations</p>
                  <p className="text-sm text-gray-400">
                    Users can start conversations when they access your shared content
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
              <CardHeader>
                <CardTitle>Chat Settings</CardTitle>
                <CardDescription>Configure your chat preferences</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">Enable Chat</p>
                      <p className="text-sm text-gray-500">Allow users to chat when viewing shared content</p>
                    </div>
                    <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-indigo-600 transition-colors">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                    </button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">Auto-respond</p>
                      <p className="text-sm text-gray-500">Send automatic welcome message to new conversations</p>
                    </div>
                    <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1" />
                    </button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">Email Notifications</p>
                      <p className="text-sm text-gray-500">Get notified via email for new messages</p>
                    </div>
                    <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-indigo-600 transition-colors">
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}