import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState } from 'react'

interface Moderator {
  id: number
  email: string
  full_name?: string
  is_active: boolean
  created_at: string
  last_login?: string
}

interface Statistics {
  totals: {
    moderators: number
    albums: number
    photos: number
    active_links: number
  }
  recent: {
    moderators: Array<{
      id: number
      email: string
      full_name?: string
      created_at: string
    }>
    albums: Array<{
      id: number
      name: string
      owner_email: string
      photo_count: number
      created_at: string
    }>
  }
}

export default function AdminDashboard() {
  const { user, logout } = useAuth()
  const [statistics, setStatistics] = useState<Statistics | null>(null)
  const [moderators, setModerators] = useState<Moderator[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'moderators' | 'content'>('overview')
  
  // Add Moderator Modal State
  const [showAddModal, setShowAddModal] = useState(false)
  const [newModeratorEmail, setNewModeratorEmail] = useState('')
  const [newModeratorName, setNewModeratorName] = useState('')
  const [newModeratorPassword, setNewModeratorPassword] = useState('')
  const [isCreating, setIsCreating] = useState(false)
  const [createError, setCreateError] = useState('')

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const token = localStorage.getItem('access_token')
      
      // Fetch statistics
      const statsResponse = await fetch('http://localhost:8000/api/admin/statistics', {
        headers: { 'Authorization': `Bearer ${token}` }
      })
      if (statsResponse.ok) {
        const statsData = await statsResponse.json()
        setStatistics(statsData)
      }

      // Fetch moderators
      const moderatorsResponse = await fetch('http://localhost:8000/api/admin/moderators', {
        headers: { 'Authorization': `Bearer ${token}` }
      })
      if (moderatorsResponse.ok) {
        const moderatorsData = await moderatorsResponse.json()
        setModerators(moderatorsData)
      }
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const createModerator = async () => {
    setIsCreating(true)
    setCreateError('')

    try {
      const token = localStorage.getItem('access_token')
      const response = await fetch('http://localhost:8000/api/admin/moderators', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          email: newModeratorEmail,
          password: newModeratorPassword,
          full_name: newModeratorName || undefined
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Failed to create moderator')
      }

      // Reset form and close modal
      setNewModeratorEmail('')
      setNewModeratorName('')
      setNewModeratorPassword('')
      setShowAddModal(false)
      
      // Refresh data
      fetchData()
    } catch (err) {
      setCreateError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsCreating(false)
    }
  }

  const handleLogout = () => {
    logout()
    window.location.href = '/'
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">S</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                  ShareX Admin
                </h1>
                <p className="text-sm text-gray-500">Administrator Dashboard</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                Welcome, {user?.full_name || user?.email}
              </span>
              <Button variant="outline" onClick={handleLogout}>
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Navigation Tabs */}
        <div className="flex space-x-1 mb-8 bg-white/60 backdrop-blur-sm rounded-xl p-1 border border-gray-200/50">
          <button
            onClick={() => setActiveTab('overview')}
            className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              activeTab === 'overview'
                ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            📊 Overview
          </button>
          <button
            onClick={() => setActiveTab('moderators')}
            className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              activeTab === 'moderators'
                ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            👥 Moderators
          </button>
          <button
            onClick={() => setActiveTab('content')}
            className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              activeTab === 'content'
                ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }`}
          >
            📁 Content
          </button>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && statistics && (
          <div className="space-y-8">
            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600">Total Moderators</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-gray-900">{statistics.totals.moderators}</div>
                  <p className="text-sm text-gray-500 mt-1">Active accounts</p>
                </CardContent>
              </Card>

              <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600">Total Albums</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-gray-900">{statistics.totals.albums}</div>
                  <p className="text-sm text-gray-500 mt-1">Photo collections</p>
                </CardContent>
              </Card>

              <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600">Total Photos</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-gray-900">{statistics.totals.photos}</div>
                  <p className="text-sm text-gray-500 mt-1">Uploaded images</p>
                </CardContent>
              </Card>

              <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-600">Active Links</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-gray-900">{statistics.totals.active_links}</div>
                  <p className="text-sm text-gray-500 mt-1">Shared content</p>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
                <CardHeader>
                  <CardTitle>Recent Moderators</CardTitle>
                  <CardDescription>Latest moderator registrations</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {statistics.recent.moderators.length > 0 ? (
                      statistics.recent.moderators.map((mod) => (
                        <div key={mod.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">{mod.full_name || mod.email}</p>
                            <p className="text-sm text-gray-500">{mod.email}</p>
                          </div>
                          <div className="text-xs text-gray-400">
                            {new Date(mod.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      ))
                    ) : (
                      <p className="text-gray-500 text-center py-4">No recent moderators</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
                <CardHeader>
                  <CardTitle>Recent Albums</CardTitle>
                  <CardDescription>Latest album creations</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {statistics.recent.albums.length > 0 ? (
                      statistics.recent.albums.map((album) => (
                        <div key={album.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">{album.name}</p>
                            <p className="text-sm text-gray-500">
                              {album.photo_count} photos • {album.owner_email}
                            </p>
                          </div>
                          <div className="text-xs text-gray-400">
                            {new Date(album.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      ))
                    ) : (
                      <p className="text-gray-500 text-center py-4">No recent albums</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Moderators Tab */}
        {activeTab === 'moderators' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">Moderator Management</h2>
              <Dialog open={showAddModal} onOpenChange={setShowAddModal}>
                <DialogTrigger asChild>
                  <Button variant="gradient">
                    + Add Moderator
                  </Button>
                </DialogTrigger>
                <DialogContent className="bg-white/95 backdrop-blur-md">
                  <DialogHeader>
                    <DialogTitle>Create New Moderator</DialogTitle>
                    <DialogDescription>
                      Add a new moderator account to the platform
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div className="space-y-4">
                    {createError && (
                      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                        {createError}
                      </div>
                    )}
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Full Name (Optional)
                      </label>
                      <input
                        type="text"
                        value={newModeratorName}
                        onChange={(e) => setNewModeratorName(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                        placeholder="Moderator's full name"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email Address
                      </label>
                      <input
                        type="email"
                        value={newModeratorEmail}
                        onChange={(e) => setNewModeratorEmail(e.target.value)}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Password
                      </label>
                      <input
                        type="password"
                        value={newModeratorPassword}
                        onChange={(e) => setNewModeratorPassword(e.target.value)}
                        required
                        minLength={6}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                        placeholder="At least 6 characters"
                      />
                    </div>
                  </div>
                  
                  <div className="flex justify-end space-x-3 mt-6">
                    <Button
                      variant="outline"
                      onClick={() => setShowAddModal(false)}
                      disabled={isCreating}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="gradient"
                      onClick={createModerator}
                      disabled={isCreating || !newModeratorEmail || !newModeratorPassword}
                    >
                      {isCreating ? 'Creating...' : 'Create Moderator'}
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
              <CardHeader>
                <CardTitle>All Moderators</CardTitle>
                <CardDescription>Manage moderator accounts and permissions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {moderators.length > 0 ? (
                    moderators.map((moderator) => (
                      <div key={moderator.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                            <span className="text-white font-medium text-sm">
                              {(moderator.full_name || moderator.email).charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">
                              {moderator.full_name || 'No name set'}
                            </p>
                            <p className="text-sm text-gray-500">{moderator.email}</p>
                            <p className="text-xs text-gray-400">
                              Joined {new Date(moderator.created_at).toLocaleDateString()}
                              {moderator.last_login && (
                                <span> • Last login {new Date(moderator.last_login).toLocaleDateString()}</span>
                              )}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            moderator.is_active 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {moderator.is_active ? 'Active' : 'Inactive'}
                          </span>
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            View Content
                          </Button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-12">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span className="text-gray-400 text-2xl">👥</span>
                      </div>
                      <p className="text-gray-500 mb-4">No moderators found</p>
                      <Button variant="gradient">
                        Create First Moderator
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Content Tab */}
        {activeTab === 'content' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Platform Content</h2>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
                <CardHeader>
                  <CardTitle>Recent Albums</CardTitle>
                  <CardDescription>Latest album activity across all moderators</CardDescription>
                </CardHeader>
                <CardContent>
                  {statistics?.recent.albums.length ? (
                    <div className="space-y-3">
                      {statistics.recent.albums.map((album) => (
                        <div key={album.id} className="p-3 bg-gray-50 rounded-lg">
                          <p className="font-medium text-gray-900">{album.name}</p>
                          <p className="text-sm text-gray-500">
                            {album.photo_count} photos • by {album.owner_email}
                          </p>
                          <p className="text-xs text-gray-400">
                            {new Date(album.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-8">No albums yet</p>
                  )}
                </CardContent>
              </Card>

              <Card className="bg-white/60 backdrop-blur-sm border-gray-200/50">
                <CardHeader>
                  <CardTitle>System Health</CardTitle>
                  <CardDescription>Platform status and performance</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Backend API</span>
                      <span className="text-green-600 font-medium">✓ Healthy</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Database</span>
                      <span className="text-green-600 font-medium">✓ Connected</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">File Storage</span>
                      <span className="text-green-600 font-medium">✓ S3 Ready</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Background Tasks</span>
                      <span className="text-green-600 font-medium">✓ Running</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}